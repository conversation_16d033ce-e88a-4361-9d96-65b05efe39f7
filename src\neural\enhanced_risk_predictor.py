"""
Enhanced Risk Prediction System for AutoGPT-Trader
Advanced ML-based risk assessment with real-time scoring and dynamic thresholds
"""

import logging
import numpy as np
import asyncio

# Optional pandas import with fallback
try:
    import pandas as pd
except ImportError:
    # Create mock pandas for fallback
    class MockPandas:
        def DataFrame(self, data=None):
            return data or {}
    pd = MockPandas()
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from decimal import Decimal
from collections import deque

# ML imports
try:
    import tensorflow as tf
    from sklearn.ensemble import IsolationForest, RandomForestRegressor
    from sklearn.preprocessing import StandardScaler, RobustScaler
    from sklearn.metrics import mean_squared_error
    import xgboost as xgb
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class RiskPrediction:
    """Risk prediction result"""
    symbol: str
    risk_score: float  # 0.0 to 1.0 (0 = low risk, 1 = high risk)
    volatility_prediction: float
    drawdown_probability: float
    liquidity_risk: float
    market_risk: float
    sentiment_risk: float
    confidence: float
    time_horizon: str  # '1h', '4h', '1d'
    timestamp: datetime
    risk_factors: Dict[str, float]
    recommendations: List[str]

class EnhancedRiskPredictor:
    """
    Advanced ML-based risk prediction system
    Combines multiple risk factors for comprehensive risk assessment
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize enhanced risk predictor"""
        self.config = config or self._default_config()
        self.models = {}
        self.scalers = {}
        self.risk_history = {}
        self.is_trained = False
        
        # Risk thresholds (dynamic)
        self.risk_thresholds = {
            'low': 0.3,
            'medium': 0.6,
            'high': 0.8,
            'critical': 0.95
        }
        
        # Feature importance tracking
        self.feature_importance = {}

        # Real-time risk tracking enhancements
        self.real_time_risk_cache = {}
        self.cache_ttl = 30  # 30 seconds cache
        self.last_cache_update = {}

        # Enhanced risk factors with weights
        self.risk_factors_weights = {
            'volatility': 0.25,
            'liquidity': 0.20,
            'sentiment': 0.15,
            'market_structure': 0.15,
            'correlation': 0.10,
            'momentum': 0.10,
            'external_factors': 0.05
        }

        # Market regime detection
        self.current_market_regime = 'normal'
        self.regime_confidence = 0.0
        self.regime_history = deque(maxlen=50)
        self.regime_thresholds = {
            'bull_market': {'momentum': 0.1, 'volatility': 0.15},
            'bear_market': {'momentum': -0.1, 'volatility': 0.20},
            'high_volatility': {'volatility': 0.30},
            'low_volatility': {'volatility': 0.05},
            'crisis': {'volatility': 0.50, 'correlation': 0.80}
        }

        # Risk prediction history for learning
        self.prediction_history = deque(maxlen=1000)
        self.accuracy_metrics = {
            'total_predictions': 0,
            'correct_predictions': 0,
            'accuracy_rate': 0.0,
            'last_updated': datetime.now(timezone.utc)
        }

        # Initialize models if ML is available
        if ML_AVAILABLE:
            self._initialize_models()
        else:
            logger.warning("ML libraries not available, using simplified risk assessment")

        logger.info("Enhanced Risk Predictor initialized with real-time capabilities and market regime detection")
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for risk prediction"""
        return {
            'lookback_window': 100,
            'prediction_horizons': ['1h', '4h', '1d'],
            'risk_factors': [
                'volatility', 'volume', 'price_momentum', 'market_sentiment',
                'liquidity', 'correlation', 'drawdown_history', 'vix_level'
            ],
            'model_types': ['isolation_forest', 'random_forest', 'xgboost', 'lstm'],
            'retraining_interval': 3600,  # 1 hour
            'confidence_threshold': 0.7,
            'dynamic_thresholds': True,
            'sentiment_weight': 0.2,
            'market_regime_detection': True
        }
    
    def _initialize_models(self):
        """Initialize ML models for risk prediction"""
        try:
            # Isolation Forest for anomaly detection
            self.models['isolation_forest'] = IsolationForest(
                contamination=0.1,
                random_state=42,
                n_jobs=-1
            )
            
            # Random Forest for risk scoring
            self.models['random_forest'] = RandomForestRegressor(
                n_estimators=200,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
            
            # XGBoost for advanced risk prediction
            self.models['xgboost'] = xgb.XGBRegressor(
                n_estimators=300,
                max_depth=8,
                learning_rate=0.01,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            )
            
            # LSTM for time series risk prediction
            if ML_AVAILABLE:
                self.models['lstm'] = self._create_lstm_risk_model()
            
            # Initialize scalers
            self.scalers['standard'] = StandardScaler()
            self.scalers['robust'] = RobustScaler()
            
            logger.info("Risk prediction models initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing risk models: {e}")
            self.models = {}
    
    def _create_lstm_risk_model(self):
        """Create LSTM model for risk prediction with dynamic input shape"""
        try:
            # Use dynamic input shape that matches actual feature extraction
            # Based on _extract_risk_features method, we have approximately 11-15 features
            expected_features = 15  # Standardized feature count

            model = tf.keras.Sequential([
                tf.keras.layers.LSTM(64, return_sequences=True, input_shape=(50, expected_features)),
                tf.keras.layers.Dropout(0.2),
                tf.keras.layers.LSTM(32, return_sequences=False),
                tf.keras.layers.Dropout(0.2),
                tf.keras.layers.Dense(16, activation='relu'),
                tf.keras.layers.Dense(1, activation='sigmoid')  # Risk score 0-1
            ])

            model.compile(
                optimizer='adam',
                loss='mse',
                metrics=['mae']
            )

            logger.info(f"LSTM risk model created with input shape: (50, {expected_features})")
            return model

        except Exception as e:
            logger.error(f"Error creating LSTM risk model: {e}")
            return None
    
    async def predict_risk(self, market_data: Dict[str, Any], symbol: str,
                          sentiment_data: Optional[Dict[str, Any]] = None) -> RiskPrediction:
        """
        Predict comprehensive risk for a trading symbol with real-time caching and market regime detection

        Args:
            market_data: Real-time market data
            symbol: Trading symbol
            sentiment_data: Optional sentiment analysis data

        Returns:
            RiskPrediction with comprehensive risk assessment
        """
        try:
            # Check cache first for performance
            cache_key = f"{symbol}_risk"
            now = datetime.now(timezone.utc)

            if (cache_key in self.real_time_risk_cache and
                cache_key in self.last_cache_update and
                (now - self.last_cache_update[cache_key]).total_seconds() < self.cache_ttl):
                cached_prediction = self.real_time_risk_cache[cache_key]
                logger.debug(f"Using cached risk prediction for {symbol}")
                return cached_prediction

            # Extract features for risk prediction
            features = await self._extract_risk_features(market_data, symbol, sentiment_data)

            if not features:
                return self._fallback_risk_prediction(symbol)

            # Detect market regime
            market_regime = self._detect_market_regime(features, market_data)

            # Adjust risk factors based on market regime
            regime_adjusted_features = self._adjust_features_for_regime(features, market_regime)

            # Get predictions from all available models
            risk_scores = {}
            confidences = {}

            if ML_AVAILABLE and self.models:
                for model_name, model in self.models.items():
                    try:
                        if model_name == 'isolation_forest':
                            # Anomaly detection
                            anomaly_score = self._predict_anomaly_risk(regime_adjusted_features, model)
                            risk_scores[model_name] = anomaly_score
                            confidences[model_name] = 0.8

                        elif model_name in ['random_forest', 'xgboost']:
                            # Regression-based risk prediction
                            risk_score = self._predict_regression_risk(regime_adjusted_features, model)
                            risk_scores[model_name] = risk_score
                            confidences[model_name] = 0.9

                        elif model_name == 'lstm' and model is not None:
                            # Time series risk prediction
                            lstm_risk = await self._predict_lstm_risk(regime_adjusted_features, model)
                            risk_scores[model_name] = lstm_risk
                            confidences[model_name] = 0.85

                    except Exception as e:
                        logger.error(f"Error with {model_name} risk prediction: {e}")
                        continue

            # Ensemble risk prediction
            if risk_scores:
                final_risk_score = self._ensemble_risk_prediction(risk_scores, confidences)
                overall_confidence = np.mean(list(confidences.values()))
            else:
                # Fallback to rule-based risk assessment
                final_risk_score = self._calculate_rule_based_risk(regime_adjusted_features)
                overall_confidence = 0.6

            # Apply market regime risk adjustment
            final_risk_score = self._apply_regime_risk_adjustment(final_risk_score, market_regime)

            # Calculate component risks
            component_risks = self._calculate_component_risks(regime_adjusted_features, sentiment_data)

            # Generate enhanced recommendations with regime context
            recommendations = self._generate_risk_recommendations(
                final_risk_score, component_risks, regime_adjusted_features, market_regime
            )

            # Update dynamic thresholds
            if self.config.get('dynamic_thresholds', True):
                self._update_dynamic_thresholds(final_risk_score, regime_adjusted_features)

            # Create risk prediction
            risk_prediction = RiskPrediction(
                symbol=symbol,
                risk_score=final_risk_score,
                volatility_prediction=component_risks['volatility'],
                drawdown_probability=component_risks['drawdown'],
                liquidity_risk=component_risks['liquidity'],
                market_risk=component_risks['market'],
                sentiment_risk=component_risks['sentiment'],
                confidence=overall_confidence,
                time_horizon='1h',  # Default horizon
                timestamp=now,
                risk_factors=regime_adjusted_features,
                recommendations=recommendations
            )

            # Add market regime information to risk factors
            risk_prediction.risk_factors['market_regime'] = market_regime['regime']
            risk_prediction.risk_factors['regime_confidence'] = market_regime['confidence']

            # Cache the prediction
            self.real_time_risk_cache[cache_key] = risk_prediction
            self.last_cache_update[cache_key] = now

            # Store prediction for learning
            self._store_prediction_for_learning(risk_prediction, features)

            return risk_prediction

        except Exception as e:
            logger.error(f"Error predicting risk for {symbol}: {e}")
            return self._fallback_risk_prediction(symbol)
    
    async def _extract_risk_features(self, market_data: Dict[str, Any], symbol: str,
                                   sentiment_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Extract features for risk prediction with enhanced sentiment analysis"""
        try:
            features = {}

            # Price-based features
            price_data = market_data.get('price_data', {})
            if price_data and symbol in price_data:
                symbol_data = price_data[symbol]

                # Volatility features
                if 'volatility' in symbol_data:
                    features['volatility'] = float(symbol_data['volatility'])

                # Price momentum
                if 'price_change_24h' in symbol_data:
                    features['price_momentum'] = float(symbol_data['price_change_24h'])

                # Volume features
                if 'volume' in symbol_data:
                    features['volume'] = float(symbol_data['volume'])

                # Spread features
                if 'bid' in symbol_data and 'ask' in symbol_data:
                    bid = float(symbol_data['bid'])
                    ask = float(symbol_data['ask'])
                    features['spread'] = (ask - bid) / bid if bid > 0 else 0.01

            # Market-wide features
            features['market_volatility'] = self._calculate_market_volatility(market_data)
            features['correlation_risk'] = self._calculate_correlation_risk(market_data, symbol)

            # Enhanced sentiment features with Fear & Greed Index
            if sentiment_data:
                features['sentiment_score'] = sentiment_data.get('aggregated_sentiment', 0.0)
                features['sentiment_confidence'] = sentiment_data.get('confidence', 0.0)
                features['sentiment_volatility'] = self._calculate_sentiment_volatility(sentiment_data)
            else:
                features['sentiment_score'] = 0.0
                features['sentiment_confidence'] = 0.0
                features['sentiment_volatility'] = 0.0

            # Fear & Greed Index risk features
            features.update(await self._extract_fear_greed_risk_features(market_data))

            # Web crawler sentiment risk features
            features.update(await self._extract_web_sentiment_risk_features(market_data))
            
            # Technical indicators
            technical_data = market_data.get('technical_indicators', {})
            if technical_data:
                features['rsi'] = technical_data.get('rsi', 50.0) / 100.0
                features['macd_signal'] = technical_data.get('macd_signal', 0.0)
                features['bollinger_position'] = technical_data.get('bollinger_position', 0.5)
            
            # Liquidity features
            order_books = market_data.get('order_books', {})
            if order_books and symbol in order_books:
                features['liquidity_score'] = self._calculate_liquidity_score(order_books[symbol])
            else:
                features['liquidity_score'] = 0.5  # Neutral
            
            # Time-based features
            now = datetime.now(timezone.utc)
            features['hour_of_day'] = now.hour / 24.0
            features['day_of_week'] = now.weekday() / 7.0
            
            # Standardize features to exactly 21 dimensions for enhanced LSTM compatibility
            return self._standardize_features(features)

        except Exception as e:
            logger.error(f"Error extracting risk features: {e}")
            return self._get_default_features()

    def _standardize_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """Standardize features to exactly 21 dimensions for enhanced neural network compatibility"""
        try:
            # Define the exact 21 features expected by the enhanced LSTM model
            expected_features = [
                'volatility', 'price_momentum', 'volume', 'spread',  # 4 price features
                'market_volatility', 'correlation_risk',  # 2 market features
                'sentiment_score', 'sentiment_confidence', 'sentiment_volatility',  # 3 sentiment features
                'rsi', 'macd_signal', 'bollinger_position',  # 3 technical features
                'liquidity_score',  # 1 liquidity feature
                'hour_of_day', 'day_of_week',  # 2 time features
                # Enhanced Fear & Greed risk features (4)
                'fear_greed_risk_score', 'fear_greed_volatility_risk', 'market_regime_risk', 'sentiment_divergence_risk',
                # Web sentiment risk features (2)
                'web_sentiment_risk', 'news_impact_risk'
            ]

            # Create standardized feature dict with defaults
            standardized = {}
            for feature_name in expected_features:
                if feature_name in features:
                    standardized[feature_name] = features[feature_name]
                else:
                    # Provide sensible defaults for missing features
                    defaults = {
                        'volatility': 0.1, 'price_momentum': 0.0, 'volume': 0.0, 'spread': 0.001,
                        'market_volatility': 0.1, 'correlation_risk': 0.5,
                        'sentiment_score': 0.0, 'sentiment_confidence': 0.0, 'sentiment_volatility': 0.0,
                        'rsi': 0.5, 'macd_signal': 0.0, 'bollinger_position': 0.5,
                        'liquidity_score': 0.5,
                        'hour_of_day': 0.5, 'day_of_week': 0.5,
                        # Enhanced Fear & Greed risk defaults
                        'fear_greed_risk_score': 0.5, 'fear_greed_volatility_risk': 0.1,
                        'market_regime_risk': 0.5, 'sentiment_divergence_risk': 0.0,
                        # Web sentiment risk defaults
                        'web_sentiment_risk': 0.0, 'news_impact_risk': 0.0
                    }
                    standardized[feature_name] = defaults.get(feature_name, 0.0)

            return standardized

        except Exception as e:
            logger.error(f"Error standardizing features: {e}")
            return self._get_default_features()

    def _get_default_features(self) -> Dict[str, float]:
        """Get default feature set with exactly 15 features"""
        return {
            'volatility': 0.1, 'price_momentum': 0.0, 'volume': 0.0, 'spread': 0.001,
            'market_volatility': 0.1, 'correlation_risk': 0.5,
            'sentiment_score': 0.0, 'sentiment_confidence': 0.0, 'sentiment_volatility': 0.0,
            'rsi': 0.5, 'macd_signal': 0.0, 'bollinger_position': 0.5,
            'liquidity_score': 0.5,
            'hour_of_day': 0.5, 'day_of_week': 0.5
        }



    def _predict_anomaly_risk(self, features: Dict[str, float], model) -> float:
        """Predict risk using anomaly detection"""
        try:
            # Check if model is fitted
            if not hasattr(model, 'estimators_'):
                logger.debug("Isolation Forest not fitted yet, using fallback prediction")
                return 0.5

            feature_array = np.array(list(features.values())).reshape(1, -1)

            # Scale features if scaler is fitted
            if 'standard' in self.scalers and hasattr(self.scalers['standard'], 'scale_'):
                feature_array = self.scalers['standard'].transform(feature_array)
            elif 'standard' in self.scalers:
                # Fit scaler with current features if not fitted
                self.scalers['standard'].fit(feature_array)
                feature_array = self.scalers['standard'].transform(feature_array)

            # Get anomaly score (-1 = anomaly, 1 = normal)
            anomaly_score = model.decision_function(feature_array)[0]

            # Convert to risk score (0-1, higher = more risk)
            risk_score = max(0.0, min(1.0, (1 - anomaly_score) / 2))

            return risk_score

        except Exception as e:
            logger.error(f"Error in anomaly risk prediction: {e}")
            return 0.5

    def _predict_regression_risk(self, features: Dict[str, float], model) -> float:
        """Predict risk using regression model"""
        try:
            # Check if model is fitted
            if not hasattr(model, 'feature_importances_') and not hasattr(model, 'coef_') and not hasattr(model, 'estimators_'):
                logger.debug("Regression model not fitted yet, using fallback prediction")
                return 0.5

            feature_array = np.array(list(features.values())).reshape(1, -1)

            # Scale features if scaler is fitted
            if 'robust' in self.scalers and hasattr(self.scalers['robust'], 'scale_'):
                feature_array = self.scalers['robust'].transform(feature_array)
            elif 'robust' in self.scalers:
                # Fit scaler with current features if not fitted
                self.scalers['robust'].fit(feature_array)
                feature_array = self.scalers['robust'].transform(feature_array)

            # Get risk prediction
            risk_score = model.predict(feature_array)[0]

            # Ensure score is between 0 and 1
            risk_score = max(0.0, min(1.0, risk_score))

            return risk_score

        except Exception as e:
            logger.error(f"Error in regression risk prediction: {e}")
            return 0.5

    async def _predict_lstm_risk(self, features: Dict[str, float], model) -> float:
        """Predict risk using LSTM model"""
        try:
            # Check if model is properly initialized and fitted
            if not hasattr(model, 'predict') or not hasattr(model, 'layers'):
                logger.debug("LSTM model not properly initialized, using fallback prediction")
                return 0.5

            # Ensure exactly 15 features for LSTM compatibility
            standardized_features = self._standardize_features(features)
            feature_array = np.array(list(standardized_features.values()))

            # Verify feature count matches LSTM input
            if len(feature_array) != 15:
                logger.warning(f"Risk feature count mismatch: got {len(feature_array)}, expected 15. Using fallback.")
                return 0.5

            # Create sequence (repeat current features for sequence length)
            sequence = np.tile(feature_array, (50, 1)).reshape(1, 50, 15)

            # Predict risk
            risk_score = model.predict(sequence)[0][0]

            return float(risk_score)

        except Exception as e:
            logger.error(f"Error in LSTM risk prediction: {e}")
            return 0.5

    def _ensemble_risk_prediction(self, risk_scores: Dict[str, float],
                                 confidences: Dict[str, float]) -> float:
        """Combine multiple risk predictions"""
        try:
            if not risk_scores:
                return 0.5

            # Weight by confidence
            total_weighted_score = 0.0
            total_weight = 0.0

            for model_name, score in risk_scores.items():
                weight = confidences.get(model_name, 0.5)
                total_weighted_score += score * weight
                total_weight += weight

            if total_weight > 0:
                return total_weighted_score / total_weight
            else:
                return np.mean(list(risk_scores.values()))

        except Exception as e:
            logger.error(f"Error in ensemble risk prediction: {e}")
            return 0.5

    def _calculate_rule_based_risk(self, features: Dict[str, float]) -> float:
        """Calculate risk using rule-based approach when ML is not available"""
        try:
            risk_components = []

            # Volatility risk
            volatility = features.get('volatility', 0.1)
            vol_risk = min(1.0, volatility / 0.5)  # Normalize to 50% volatility
            risk_components.append(vol_risk * 0.3)

            # Price momentum risk
            momentum = abs(features.get('price_momentum', 0.0))
            momentum_risk = min(1.0, momentum / 0.2)  # Normalize to 20% change
            risk_components.append(momentum_risk * 0.2)

            # Spread risk
            spread = features.get('spread', 0.001)
            spread_risk = min(1.0, spread / 0.01)  # Normalize to 1% spread
            risk_components.append(spread_risk * 0.2)

            # Sentiment risk
            sentiment_score = features.get('sentiment_score', 0.0)
            sentiment_risk = abs(sentiment_score)  # Extreme sentiment = higher risk
            risk_components.append(sentiment_risk * 0.15)

            # Liquidity risk
            liquidity = features.get('liquidity_score', 0.5)
            liquidity_risk = 1.0 - liquidity  # Lower liquidity = higher risk
            risk_components.append(liquidity_risk * 0.15)

            return sum(risk_components)

        except Exception as e:
            logger.error(f"Error in rule-based risk calculation: {e}")
            return 0.5

    def _calculate_component_risks(self, features: Dict[str, float],
                                  sentiment_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Calculate individual risk components"""
        try:
            components = {}

            # Volatility risk
            volatility = features.get('volatility', 0.1)
            components['volatility'] = min(1.0, volatility / 0.3)

            # Drawdown probability
            momentum = features.get('price_momentum', 0.0)
            vol = features.get('volatility', 0.1)
            components['drawdown'] = min(1.0, (abs(momentum) + vol) / 0.4)

            # Liquidity risk
            components['liquidity'] = 1.0 - features.get('liquidity_score', 0.5)

            # Market risk
            market_vol = features.get('market_volatility', 0.1)
            correlation = features.get('correlation_risk', 0.5)
            components['market'] = min(1.0, (market_vol + correlation) / 2)

            # Sentiment risk
            if sentiment_data:
                sentiment_score = sentiment_data.get('aggregated_sentiment', 0.0)
                sentiment_confidence = sentiment_data.get('confidence', 0.0)
                # High absolute sentiment with low confidence = higher risk
                components['sentiment'] = abs(sentiment_score) * (1.0 - sentiment_confidence)
            else:
                components['sentiment'] = 0.3  # Default moderate risk

            return components

        except Exception as e:
            logger.error(f"Error calculating component risks: {e}")
            return {
                'volatility': 0.5,
                'drawdown': 0.5,
                'liquidity': 0.5,
                'market': 0.5,
                'sentiment': 0.5
            }

    def _generate_risk_recommendations(self, risk_score: float,
                                     component_risks: Dict[str, float],
                                     features: Dict[str, float],
                                     market_regime: Optional[Dict[str, Any]] = None) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []

        try:
            # Overall risk level recommendations
            if risk_score > self.risk_thresholds['critical']:
                recommendations.append("CRITICAL: Halt all trading immediately")
                recommendations.append("Review market conditions before resuming")
            elif risk_score > self.risk_thresholds['high']:
                recommendations.append("HIGH RISK: Reduce position sizes by 50%")
                recommendations.append("Implement tight stop losses")
            elif risk_score > self.risk_thresholds['medium']:
                recommendations.append("MEDIUM RISK: Use conservative position sizing")
                recommendations.append("Monitor positions closely")
            else:
                recommendations.append("LOW RISK: Normal trading conditions")

            # Component-specific recommendations
            if component_risks['volatility'] > 0.7:
                recommendations.append("High volatility detected - use wider stop losses")

            if component_risks['liquidity'] > 0.6:
                recommendations.append("Low liquidity - avoid large orders")
                recommendations.append("Consider using limit orders instead of market orders")

            if component_risks['sentiment'] > 0.7:
                recommendations.append("Extreme sentiment detected - consider contrarian positions")

            if component_risks['drawdown'] > 0.8:
                recommendations.append("High drawdown probability - implement protective stops")

            # Feature-specific recommendations
            spread = features.get('spread', 0.001)
            if spread > 0.005:  # 0.5% spread
                recommendations.append("Wide spreads detected - wait for better conditions")

            return recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["Unable to generate recommendations - use default risk management"]

    def _calculate_market_volatility(self, market_data: Dict[str, Any]) -> float:
        """Calculate overall market volatility"""
        try:
            price_data = market_data.get('price_data', {})
            if not price_data:
                return 0.1  # Default volatility

            volatilities = []
            for symbol, data in price_data.items():
                if isinstance(data, dict) and 'volatility' in data:
                    volatilities.append(float(data['volatility']))

            if volatilities:
                return np.mean(volatilities)
            else:
                return 0.1

        except Exception as e:
            logger.error(f"Error calculating market volatility: {e}")
            return 0.1

    def _calculate_correlation_risk(self, market_data: Dict[str, Any], symbol: str) -> float:
        """Calculate correlation risk with market"""
        try:
            # Simplified correlation risk calculation
            # In a real implementation, this would use historical price correlations
            price_data = market_data.get('price_data', {})

            if not price_data or symbol not in price_data:
                return 0.5  # Neutral correlation risk

            symbol_momentum = price_data[symbol].get('price_change_24h', 0.0)

            # Calculate average market momentum
            market_momentums = []
            for sym, data in price_data.items():
                if sym != symbol and isinstance(data, dict):
                    momentum = data.get('price_change_24h', 0.0)
                    market_momentums.append(momentum)

            if market_momentums:
                market_momentum = np.mean(market_momentums)
                # High correlation = higher risk during market stress
                correlation = abs(symbol_momentum - market_momentum) / (abs(symbol_momentum) + abs(market_momentum) + 0.01)
                return 1.0 - correlation  # Lower correlation = lower risk

            return 0.5

        except Exception as e:
            logger.error(f"Error calculating correlation risk: {e}")
            return 0.5

    def _calculate_sentiment_volatility(self, sentiment_data: Dict[str, Any]) -> float:
        """Calculate sentiment volatility"""
        try:
            # This would ideally use historical sentiment data
            # For now, use confidence as a proxy for volatility
            confidence = sentiment_data.get('confidence', 0.5)
            return 1.0 - confidence  # Low confidence = high volatility

        except Exception as e:
            logger.error(f"Error calculating sentiment volatility: {e}")
            return 0.5

    def _calculate_liquidity_score(self, order_book: Dict[str, Any]) -> float:
        """Calculate liquidity score from order book"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])

            if not bids or not asks:
                return 0.3  # Low liquidity

            # Calculate bid-ask spread
            best_bid = float(bids[0][0]) if bids else 0
            best_ask = float(asks[0][0]) if asks else 0

            if best_bid > 0 and best_ask > 0:
                spread = (best_ask - best_bid) / best_bid

                # Calculate depth (total volume in top 5 levels)
                bid_depth = sum(float(bid[1]) for bid in bids[:5])
                ask_depth = sum(float(ask[1]) for ask in asks[:5])
                total_depth = bid_depth + ask_depth

                # Liquidity score based on spread and depth
                spread_score = max(0, 1 - spread / 0.01)  # Normalize to 1% spread
                depth_score = min(1, total_depth / 1000)  # Normalize to 1000 units

                return (spread_score + depth_score) / 2

            return 0.3

        except Exception as e:
            logger.error(f"Error calculating liquidity score: {e}")
            return 0.3

    def _update_dynamic_thresholds(self, risk_score: float, features: Dict[str, float]):
        """Update dynamic risk thresholds based on market conditions"""
        try:
            # Adjust thresholds based on market volatility
            market_vol = features.get('market_volatility', 0.1)

            # Higher volatility = lower thresholds (more conservative)
            vol_adjustment = market_vol / 0.2  # Normalize to 20% volatility

            base_thresholds = {
                'low': 0.3,
                'medium': 0.6,
                'high': 0.8,
                'critical': 0.95
            }

            for level, threshold in base_thresholds.items():
                adjusted = threshold * (1 - vol_adjustment * 0.2)  # Max 20% adjustment
                self.risk_thresholds[level] = max(0.1, min(0.95, adjusted))

        except Exception as e:
            logger.error(f"Error updating dynamic thresholds: {e}")

    def _detect_market_regime(self, features: Dict[str, float], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect current market regime based on features"""
        try:
            volatility = features.get('volatility', 0.1)
            momentum = features.get('price_momentum', 0.0)
            market_vol = features.get('market_volatility', 0.1)
            correlation = features.get('correlation_risk', 0.5)

            regime = 'normal'
            confidence = 0.5

            # Crisis detection
            if volatility > self.regime_thresholds['crisis']['volatility'] or correlation > self.regime_thresholds['crisis']['correlation']:
                regime = 'crisis'
                confidence = 0.9
            # High volatility regime
            elif volatility > self.regime_thresholds['high_volatility']['volatility']:
                regime = 'high_volatility'
                confidence = 0.8
            # Bull market detection
            elif momentum > self.regime_thresholds['bull_market']['momentum'] and volatility < self.regime_thresholds['bull_market']['volatility']:
                regime = 'bull_market'
                confidence = 0.7
            # Bear market detection
            elif momentum < self.regime_thresholds['bear_market']['momentum'] and volatility > self.regime_thresholds['bear_market']['volatility']:
                regime = 'bear_market'
                confidence = 0.7
            # Low volatility regime
            elif volatility < self.regime_thresholds['low_volatility']['volatility']:
                regime = 'low_volatility'
                confidence = 0.6

            # Update regime history
            regime_data = {
                'regime': regime,
                'confidence': confidence,
                'timestamp': datetime.now(timezone.utc),
                'features': features.copy()
            }
            self.regime_history.append(regime_data)

            # Update current regime
            self.current_market_regime = regime
            self.regime_confidence = confidence

            return regime_data

        except Exception as e:
            logger.error(f"Error detecting market regime: {e}")
            return {'regime': 'normal', 'confidence': 0.5, 'timestamp': datetime.now(timezone.utc)}

    def _adjust_features_for_regime(self, features: Dict[str, float], market_regime: Dict[str, Any]) -> Dict[str, float]:
        """Adjust risk features based on detected market regime"""
        try:
            adjusted_features = features.copy()
            regime = market_regime.get('regime', 'normal')

            # Regime-specific adjustments
            if regime == 'crisis':
                # Increase all risk factors during crisis
                adjusted_features['volatility'] = min(1.0, adjusted_features.get('volatility', 0.1) * 1.5)
                adjusted_features['correlation_risk'] = min(1.0, adjusted_features.get('correlation_risk', 0.5) * 1.3)
                adjusted_features['liquidity_score'] = max(0.0, adjusted_features.get('liquidity_score', 0.5) * 0.7)

            elif regime == 'high_volatility':
                # Increase volatility-related risks
                adjusted_features['volatility'] = min(1.0, adjusted_features.get('volatility', 0.1) * 1.2)
                adjusted_features['spread'] = min(1.0, adjusted_features.get('spread', 0.001) * 1.3)

            elif regime == 'bull_market':
                # Reduce some risks during bull markets but increase momentum risk
                adjusted_features['sentiment_score'] = max(-1.0, min(1.0, adjusted_features.get('sentiment_score', 0.0) + 0.1))
                adjusted_features['price_momentum'] = abs(adjusted_features.get('price_momentum', 0.0)) * 1.1

            elif regime == 'bear_market':
                # Increase risks during bear markets
                adjusted_features['sentiment_score'] = max(-1.0, min(1.0, adjusted_features.get('sentiment_score', 0.0) - 0.1))
                adjusted_features['drawdown_risk'] = min(1.0, adjusted_features.get('volatility', 0.1) * 1.2)

            elif regime == 'low_volatility':
                # Reduce volatility-based risks but increase complacency risk
                adjusted_features['volatility'] = adjusted_features.get('volatility', 0.1) * 0.8
                adjusted_features['complacency_risk'] = 0.3  # Add complacency factor

            return adjusted_features

        except Exception as e:
            logger.error(f"Error adjusting features for regime: {e}")
            return features

    def _apply_regime_risk_adjustment(self, risk_score: float, market_regime: Dict[str, Any]) -> float:
        """Apply final risk adjustment based on market regime"""
        try:
            regime = market_regime.get('regime', 'normal')
            confidence = market_regime.get('confidence', 0.5)

            # Regime-specific risk multipliers
            regime_multipliers = {
                'crisis': 1.5,
                'high_volatility': 1.2,
                'bear_market': 1.1,
                'normal': 1.0,
                'bull_market': 0.9,
                'low_volatility': 0.8
            }

            multiplier = regime_multipliers.get(regime, 1.0)

            # Apply confidence weighting
            adjusted_multiplier = 1.0 + (multiplier - 1.0) * confidence

            # Apply adjustment
            adjusted_risk = risk_score * adjusted_multiplier

            # Ensure bounds
            return max(0.0, min(1.0, adjusted_risk))

        except Exception as e:
            logger.error(f"Error applying regime risk adjustment: {e}")
            return risk_score

    def _store_prediction_for_learning(self, risk_prediction: RiskPrediction, features: Dict[str, float]):
        """Store prediction for continuous learning"""
        try:
            prediction_data = {
                'timestamp': risk_prediction.timestamp,
                'symbol': risk_prediction.symbol,
                'predicted_risk': risk_prediction.risk_score,
                'confidence': risk_prediction.confidence,
                'features': features.copy(),
                'market_regime': self.current_market_regime,
                'regime_confidence': self.regime_confidence
            }

            self.prediction_history.append(prediction_data)

            # Update accuracy metrics
            self.accuracy_metrics['total_predictions'] += 1
            self.accuracy_metrics['last_updated'] = datetime.now(timezone.utc)

            logger.debug(f"Stored prediction for learning: {risk_prediction.symbol} - Risk: {risk_prediction.risk_score:.3f}")

        except Exception as e:
            logger.error(f"Error storing prediction for learning: {e}")

    def get_prediction_accuracy_metrics(self) -> Dict[str, Any]:
        """Get prediction accuracy metrics"""
        try:
            return {
                'total_predictions': self.accuracy_metrics['total_predictions'],
                'correct_predictions': self.accuracy_metrics['correct_predictions'],
                'accuracy_rate': self.accuracy_metrics['accuracy_rate'],
                'last_updated': self.accuracy_metrics['last_updated'].isoformat(),
                'current_regime': self.current_market_regime,
                'regime_confidence': self.regime_confidence,
                'prediction_history_size': len(self.prediction_history)
            }
        except Exception as e:
            logger.error(f"Error getting accuracy metrics: {e}")
            return {'error': str(e)}

    def _fallback_risk_prediction(self, symbol: str) -> RiskPrediction:
        """ELIMINATED: NO FALLBACK RISK PREDICTIONS - SYSTEM MUST USE REAL NEURAL MODELS"""
        # CRITICAL: Fallback risk predictions have been eliminated to ensure only real neural models are used
        # The system must fail gracefully if neural models are unavailable rather than using simplified fallbacks

        logger.error(f"[CRITICAL] Neural risk prediction failed for {symbol} - NO FALLBACK ALLOWED")
        logger.error("[CRITICAL] System requires functional neural models - check model initialization")

        # Raise exception instead of providing fallback prediction
        raise RuntimeError(f"Neural risk prediction failed for {symbol} - no fallback available")

    def get_risk_thresholds(self) -> Dict[str, float]:
        """Get current risk thresholds"""
        return self.risk_thresholds.copy()

    def update_risk_history(self, symbol: str, risk_prediction: RiskPrediction):
        """Update risk history for learning"""
        try:
            if symbol not in self.risk_history:
                self.risk_history[symbol] = []

            self.risk_history[symbol].append({
                'timestamp': risk_prediction.timestamp,
                'risk_score': risk_prediction.risk_score,
                'confidence': risk_prediction.confidence
            })

            # Keep only last 1000 predictions
            if len(self.risk_history[symbol]) > 1000:
                self.risk_history[symbol] = self.risk_history[symbol][-1000:]

        except Exception as e:
            logger.error(f"Error updating risk history: {e}")

    async def _extract_fear_greed_risk_features(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract Fear & Greed Index risk features"""
        try:
            features = {}

            # Extract Fear & Greed Index from live data
            live_aggregated = market_data.get('live_aggregated', {})
            fear_greed_index = live_aggregated.get('fear_greed_index', 65.0)

            # Fear & Greed risk score (extreme values indicate higher risk)
            if fear_greed_index <= 25 or fear_greed_index >= 75:
                features['fear_greed_risk_score'] = 0.8  # High risk at extremes
            elif fear_greed_index <= 35 or fear_greed_index >= 65:
                features['fear_greed_risk_score'] = 0.6  # Medium risk
            else:
                features['fear_greed_risk_score'] = 0.3  # Low risk in neutral zone

            # Fear & Greed volatility risk (rapid changes indicate instability)
            if not hasattr(self, 'fear_greed_history'):
                self.fear_greed_history = []

            self.fear_greed_history.append(fear_greed_index)
            if len(self.fear_greed_history) > 24:  # Keep 24 hours
                self.fear_greed_history = self.fear_greed_history[-24:]

            if len(self.fear_greed_history) >= 3:
                volatility = np.std(self.fear_greed_history[-3:]) / 100.0
                features['fear_greed_volatility_risk'] = min(1.0, volatility * 5)  # Amplify for sensitivity
            else:
                features['fear_greed_volatility_risk'] = 0.1

            # Market regime risk (extreme greed = bubble risk, extreme fear = crash risk)
            if fear_greed_index >= 80:
                features['market_regime_risk'] = 0.9  # Bubble risk
            elif fear_greed_index <= 20:
                features['market_regime_risk'] = 0.7  # Crash risk (but potential opportunity)
            else:
                features['market_regime_risk'] = 0.3  # Normal risk

            # Sentiment divergence risk (when Fear & Greed diverges from price action)
            price_data = market_data.get('price_data', {})
            if price_data:
                avg_price_change = np.mean([
                    float(data.get('price_change_24h', 0))
                    for data in price_data.values()
                    if 'price_change_24h' in data
                ])

                # Normalize Fear & Greed to -1 to 1 scale
                normalized_fg = (fear_greed_index - 50) / 50.0

                # Calculate divergence (high divergence = high risk)
                divergence = abs(normalized_fg - (avg_price_change / 100.0))
                features['sentiment_divergence_risk'] = min(1.0, divergence * 2)
            else:
                features['sentiment_divergence_risk'] = 0.0

            return features

        except Exception as e:
            logger.error(f"Error extracting Fear & Greed risk features: {e}")
            return {
                'fear_greed_risk_score': 0.5,
                'fear_greed_volatility_risk': 0.1,
                'market_regime_risk': 0.5,
                'sentiment_divergence_risk': 0.0
            }

    async def _extract_web_sentiment_risk_features(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract web crawler sentiment risk features"""
        try:
            features = {}

            # Web crawler insights
            web_insights = market_data.get('web_crawler_insights', {})

            if web_insights:
                # Web sentiment risk (negative sentiment = higher risk)
                sentiment_score = web_insights.get('overall_sentiment', 0.0)
                if sentiment_score < -0.5:
                    features['web_sentiment_risk'] = 0.8  # High risk for very negative sentiment
                elif sentiment_score < -0.2:
                    features['web_sentiment_risk'] = 0.6  # Medium risk
                elif sentiment_score > 0.5:
                    features['web_sentiment_risk'] = 0.4  # Some risk for extreme optimism
                else:
                    features['web_sentiment_risk'] = 0.2  # Low risk for neutral sentiment

                # News impact risk (high impact news = higher volatility risk)
                high_impact_count = web_insights.get('high_impact_count', 0)
                features['news_impact_risk'] = min(1.0, high_impact_count / 5.0)  # Normalize to 0-1
            else:
                features['web_sentiment_risk'] = 0.0
                features['news_impact_risk'] = 0.0

            return features

        except Exception as e:
            logger.error(f"Error extracting web sentiment risk features: {e}")
            return {
                'web_sentiment_risk': 0.0,
                'news_impact_risk': 0.0
            }

# Export the main class
__all__ = ['EnhancedRiskPredictor', 'RiskPrediction']
