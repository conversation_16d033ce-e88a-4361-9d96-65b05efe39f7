#!/usr/bin/env python3
"""
Test suite for Live Data Fetcher integration with AutoGPT Trader
"""

import asyncio
import pytest
import logging
import time
from unittest.mock import Mock, patch
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.data_feeds.live_data_fetcher import (
    LiveDataFetcher, 
    create_default_fetcher,
    CoinGeckoProvider,
    BinanceProvider,
    BybitProvider,
    ErrorSolver,
    remove_outliers,
    calculate_price_momentum,
    calculate_volatility
)

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestUtilityFunctions:
    """Test utility functions"""
    
    def test_remove_outliers(self):
        """Test outlier removal function"""
        # Normal case
        values = [1.0, 1.1, 1.2, 1.1, 1.0, 10.0]  # 10.0 is outlier
        clean = remove_outliers(values, z_threshold=2.0)
        assert 10.0 not in clean
        assert len(clean) == 5
        
        # Edge cases
        assert remove_outliers([]) == []
        assert remove_outliers([1.0]) == [1.0]
        assert remove_outliers([1.0, 1.0, 1.0]) == [1.0, 1.0, 1.0]  # No variance
    
    def test_calculate_price_momentum(self):
        """Test price momentum calculation"""
        assert calculate_price_momentum(110, 100) == 10.0  # 10% increase
        assert calculate_price_momentum(90, 100) == -10.0   # 10% decrease
        assert calculate_price_momentum(100, 0) == 0.0      # Division by zero
    
    def test_calculate_volatility(self):
        """Test volatility calculation"""
        prices = [100, 105, 95, 110, 90]
        vol = calculate_volatility(prices)
        assert vol > 0
        
        # Edge cases
        assert calculate_volatility([]) == 0.0
        assert calculate_volatility([100]) == 0.0

class TestErrorSolver:
    """Test error solving functionality"""
    
    def test_error_solver_timeout(self):
        """Test timeout error solving"""
        solver = ErrorSolver()
        
        # Simulate timeout error
        exc = Exception("TimeoutError: Request timed out")
        result = asyncio.run(solver.solve("TestProvider", exc))
        
        assert result is True
        assert "timeout" in solver.adjustments["TestProvider"]
    
    def test_error_solver_rate_limit(self):
        """Test rate limit error solving"""
        solver = ErrorSolver()
        
        # Simulate rate limit error
        exc = Exception("429 Too Many Requests")
        result = asyncio.run(solver.solve("TestProvider", exc))
        
        assert result is True
        assert "backoff" in solver.adjustments["TestProvider"]
    
    def test_error_stats(self):
        """Test error statistics tracking"""
        solver = ErrorSolver()
        
        # Add some errors
        asyncio.run(solver.solve("TestProvider", Exception("error1")))
        asyncio.run(solver.solve("TestProvider", Exception("error2")))
        
        stats = solver.get_error_stats("TestProvider")
        assert stats["total_errors"] == 2
        assert stats["recent_errors"] == 2

class TestProviders:
    """Test data provider functionality"""
    
    @pytest.mark.asyncio
    async def test_coingecko_provider_mock(self):
        """Test CoinGecko provider with mocked response"""
        solver = ErrorSolver()
        provider = CoinGeckoProvider(['bitcoin'], ['usd'], solver)
        
        # Mock successful response
        mock_data = {
            'bitcoin': {
                'usd': 50000.0,
                'usd_24h_change': 5.0,
                'usd_market_cap': **********
            }
        }
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_response = Mock()
            mock_response.raise_for_status.return_value = None
            mock_response.json.return_value = mock_data
            mock_get.return_value.__aenter__.return_value = mock_response
            
            session = Mock()
            result = await provider.fetch(session)
            
            assert 'bitcoin_usd' in result
            assert result['bitcoin_usd'] == 50000.0
            assert 'bitcoin_usd_24h_change' in result
    
    @pytest.mark.asyncio
    async def test_provider_caching(self):
        """Test provider caching functionality"""
        solver = ErrorSolver()
        provider = CoinGeckoProvider(['bitcoin'], ['usd'], solver)
        
        # Set cache
        provider.cache = {'bitcoin_usd': 50000.0}
        provider.last_fetch_time = time.time()
        
        # Should return cached data
        session = Mock()
        result = await provider.fetch_with_cache(session)
        assert result == {'bitcoin_usd': 50000.0}
        
        # Test cache expiry
        provider.last_fetch_time = time.time() - 100  # Old cache
        provider.cache = {}
        result = await provider.fetch_with_cache(session)
        assert result == {}  # Empty because fetch will fail without mock

class TestLiveDataFetcher:
    """Test main LiveDataFetcher functionality"""
    
    def test_create_default_fetcher(self):
        """Test default fetcher creation"""
        fetcher = create_default_fetcher()
        
        assert isinstance(fetcher, LiveDataFetcher)
        assert len(fetcher.providers) > 0
        assert any(isinstance(p, CoinGeckoProvider) for p in fetcher.providers)
        assert any(isinstance(p, BinanceProvider) for p in fetcher.providers)
    
    def test_fetcher_configuration(self):
        """Test fetcher with custom configuration"""
        config = {
            'coingecko': {
                'enabled': True,
                'ids': ['bitcoin'],
                'vs_currencies': ['usd']
            },
            'binance': {'enabled': False},
            'bybit': {'enabled': False}
        }
        
        fetcher = LiveDataFetcher(config)
        assert len(fetcher.providers) == 1
        assert isinstance(fetcher.providers[0], CoinGeckoProvider)
    
    @pytest.mark.asyncio
    async def test_gather_live_data_mock(self):
        """Test data gathering with mocked providers"""
        fetcher = create_default_fetcher()
        
        # Mock all providers to return test data
        for provider in fetcher.providers:
            provider.fetch_with_cache = Mock(return_value={
                f'{provider.name.lower()}_btc_usd': 50000.0,
                f'{provider.name.lower()}_eth_usd': 3000.0
            })
        
        result = await fetcher.gather_live_data()
        
        assert 'live_aggregated' in result
        assert 'statistics' in result
        assert 'provider_stats' in result
        assert 'data_quality' in result
        assert result['timestamp'] > 0
    
    def test_get_latest_prices(self):
        """Test price retrieval functionality"""
        fetcher = create_default_fetcher()
        
        # Set mock data
        fetcher.last_payload = {
            'live_aggregated': {
                'bitcoin_usd': 50000.0,
                'ethereum_usd': 3000.0,
                'binance_btcusdt': 50100.0
            }
        }
        
        # Test getting all prices
        all_prices = fetcher.get_latest_prices()
        assert len(all_prices) == 3
        
        # Test getting specific symbols
        btc_prices = fetcher.get_latest_prices(['bitcoin_usd'])
        assert 'bitcoin_usd' in btc_prices
        assert len(btc_prices) == 1
        
        # Test partial matching
        btc_partial = fetcher.get_latest_prices(['btc'])
        assert len(btc_partial) > 0  # Should find btc-related symbols
    
    def test_data_quality_score(self):
        """Test data quality scoring"""
        fetcher = create_default_fetcher()
        
        # Mock quality data
        fetcher.last_payload = {
            'data_quality': {
                'total_providers': 3,
                'successful_providers': 2,
                'total_data_points': 50
            }
        }
        
        score = fetcher.get_data_quality_score()
        assert 0 <= score <= 1
        assert score > 0  # Should have some quality

class TestIntegration:
    """Test integration with AutoGPT Trader system"""
    
    @pytest.mark.asyncio
    async def test_web_api_endpoints(self):
        """Test web API functionality"""
        fetcher = create_default_fetcher()
        
        # Set some test data
        fetcher.last_payload = {
            'live_aggregated': {'btc_usd': 50000},
            'timestamp': time.time()
        }
        
        # Start web API
        await fetcher.serve_web_api(host='127.0.0.1', port=8081)
        
        try:
            # Test endpoints (would need actual HTTP client in real test)
            # This is a basic structure test
            assert fetcher.app is not None
            assert fetcher.runner is not None
            
        finally:
            # Cleanup
            await fetcher.stop_web_api()
    
    def test_price_momentum_tracking(self):
        """Test price momentum tracking"""
        fetcher = create_default_fetcher()
        
        # Simulate price history
        fetcher.price_history = {
            'btc_usd': [49000, 50000, 51000]  # Upward trend
        }
        
        # Mock momentum data in payload
        fetcher.last_payload = {
            'momentum': {
                'btc_usd_momentum': 2.04  # ~2% increase
            }
        }
        
        momentum = fetcher.get_price_momentum('btc_usd')
        assert momentum is not None
        assert momentum > 0  # Positive momentum

if __name__ == '__main__':
    # Run basic tests
    logger.info("🧪 Running Live Data Fetcher tests...")
    
    # Test utility functions
    test_utils = TestUtilityFunctions()
    test_utils.test_remove_outliers()
    test_utils.test_calculate_price_momentum()
    test_utils.test_calculate_volatility()
    logger.info("✅ Utility function tests passed")
    
    # Test error solver
    test_solver = TestErrorSolver()
    test_solver.test_error_solver_timeout()
    test_solver.test_error_solver_rate_limit()
    test_solver.test_error_stats()
    logger.info("✅ Error solver tests passed")
    
    # Test fetcher
    test_fetcher = TestLiveDataFetcher()
    test_fetcher.test_create_default_fetcher()
    test_fetcher.test_fetcher_configuration()
    test_fetcher.test_get_latest_prices()
    test_fetcher.test_data_quality_score()
    logger.info("✅ Live data fetcher tests passed")
    
    logger.info("🎉 All tests completed successfully!")
