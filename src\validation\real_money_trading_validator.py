#!/usr/bin/env python3
"""
Real Money Trading Validator
Comprehensive validation system to ensure ONLY real money trading with live data
"""

import os
import time
import logging
import asyncio
from typing import Dict, List, Any, Optional
from decimal import Decimal
from datetime import datetime

logger = logging.getLogger(__name__)

class RealMoneyTradingValidator:
    """
    Enterprise-grade validator to ensure ONLY real money trading with live data
    System fails completely rather than falling back to simulation/fake data
    """
    
    def __init__(self):
        self.validation_enabled = True
        self.fail_fast_mode = True
        self.validation_history = []
        self.last_validation_time = 0
        self.validation_interval = 30  # Validate every 30 seconds
        
        # Prohibited patterns that indicate fake/test data
        self.prohibited_patterns = {
            'fake_prices': [50000.0, 3000.0, 100.0, 0.5, 300.0, 0.6, 0.1],  # Hardcoded fallback prices
            'test_balances': [10000.0, 1000.0, 100.0],  # Common test balance amounts
            'simulation_keywords': ['test', 'mock', 'fake', 'simulation', 'demo', 'sandbox'],
            'hardcoded_symbols': ['BTC-USD', 'ETH-USD', 'SOL-USD'],  # Hardcoded trading pairs
        }
        
        # Real-time data requirements
        self.max_price_age_seconds = 60  # Price data must be less than 1 minute old
        self.min_price_sources = 1  # Minimum number of price sources required
        self.balance_change_threshold = 0.000001  # Minimum detectable balance change
        
    async def validate_real_money_trading_environment(self) -> Dict[str, Any]:
        """
        Comprehensive validation of real money trading environment
        Returns validation results or raises exception if critical issues found
        """
        try:
            logger.info("🔍 [REAL-MONEY-VALIDATOR] Starting comprehensive real money trading validation...")
            
            validation_results = {
                'timestamp': time.time(),
                'overall_valid': True,
                'critical_issues': [],
                'warnings': [],
                'validations': {}
            }
            
            # 1. Environment Variable Validation
            env_validation = await self._validate_environment_variables()
            validation_results['validations']['environment'] = env_validation
            if not env_validation['valid']:
                validation_results['critical_issues'].extend(env_validation['issues'])
                validation_results['overall_valid'] = False
            
            # 2. API Endpoint Validation
            api_validation = await self._validate_api_endpoints()
            validation_results['validations']['api_endpoints'] = api_validation
            if not api_validation['valid']:
                validation_results['critical_issues'].extend(api_validation['issues'])
                validation_results['overall_valid'] = False
            
            # 3. Data Source Validation
            data_validation = await self._validate_data_sources()
            validation_results['validations']['data_sources'] = data_validation
            if not data_validation['valid']:
                validation_results['critical_issues'].extend(data_validation['issues'])
                validation_results['overall_valid'] = False
            
            # 4. Trading Configuration Validation
            config_validation = await self._validate_trading_configuration()
            validation_results['validations']['trading_config'] = config_validation
            if not config_validation['valid']:
                validation_results['critical_issues'].extend(config_validation['issues'])
                validation_results['overall_valid'] = False
            
            # Store validation history
            self.validation_history.append(validation_results)
            self.last_validation_time = time.time()
            
            # CRITICAL: Fail fast if any critical issues found
            if not validation_results['overall_valid'] and self.fail_fast_mode:
                critical_issues_str = '; '.join(validation_results['critical_issues'])
                error_msg = f"CRITICAL: Real money trading validation FAILED. Issues: {critical_issues_str}"
                logger.error(f"❌ [REAL-MONEY-VALIDATOR] {error_msg}")
                raise RuntimeError(error_msg)
            
            if validation_results['overall_valid']:
                logger.info("✅ [REAL-MONEY-VALIDATOR] All validations passed - real money trading environment verified")
            else:
                logger.warning(f"⚠️ [REAL-MONEY-VALIDATOR] Validation completed with warnings: {validation_results['warnings']}")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ [REAL-MONEY-VALIDATOR] Validation failed: {e}")
            if self.fail_fast_mode:
                raise RuntimeError(f"Real money trading validation failed: {e}")
            return {'overall_valid': False, 'error': str(e)}
    
    async def _validate_environment_variables(self) -> Dict[str, Any]:
        """Validate environment variables for real money trading"""
        try:
            issues = []
            warnings = []

            # Check for prohibited test/simulation modes
            prohibited_vars = {
                'TEST_MODE': ['true', '1', 'yes', 'on'],
                'DEMO_MODE': ['true', '1', 'yes', 'on'],
                'SIMULATION_MODE': ['true', '1', 'yes', 'on'],
                'MOCK_MODE': ['true', '1', 'yes', 'on'],
                'SANDBOX': ['true', '1', 'yes', 'on'],
                'TESTNET': ['true', '1', 'yes', 'on'],
                'DRY_RUN': ['true', '1', 'yes', 'on'],
                'PAPER_TRADING': ['true', '1', 'yes', 'on'],
                'FAKE_TRADING': ['true', '1', 'yes', 'on'],
                'VIRTUAL_TRADING': ['true', '1', 'yes', 'on'],
                'BACKTEST_MODE': ['true', '1', 'yes', 'on'],
                'SIMULATION': ['true', '1', 'yes', 'on']
            }

            for var, prohibited_values in prohibited_vars.items():
                value = os.getenv(var, '').lower()
                if value in prohibited_values:
                    issues.append(f"Prohibited environment variable {var}={value} detected")
            
            # Check for required live trading variables
            required_vars = {
                'LIVE_TRADING': ['true', '1', 'yes', 'on'],
                'REAL_MONEY_TRADING': ['true', '1', 'yes', 'on']
            }
            
            for var, required_values in required_vars.items():
                value = os.getenv(var, '').lower()
                if value not in required_values:
                    issues.append(f"Required environment variable {var} not set to live trading mode")
            
            # Check API credentials are present
            api_credentials = ['BYBIT_API_KEY', 'BYBIT_API_SECRET']
            for cred in api_credentials:
                if not os.getenv(cred):
                    issues.append(f"Missing API credential: {cred}")
            
            return {
                'valid': len(issues) == 0,
                'issues': issues,
                'warnings': warnings,
                'checked_variables': len(prohibited_vars) + len(required_vars) + len(api_credentials)
            }
            
        except Exception as e:
            return {'valid': False, 'issues': [f"Environment validation error: {e}"]}
    
    async def _validate_api_endpoints(self) -> Dict[str, Any]:
        """Validate that API endpoints are pointing to live trading (not testnet)"""
        try:
            issues = []
            warnings = []
            
            # Check for testnet URLs in common configurations
            testnet_indicators = [
                'testnet', 'test', 'sandbox', 'demo', 'api-testnet',
                'test.bybit.com', 'sandbox.coinbase.com'
            ]
            
            # Check environment variables for testnet URLs
            url_vars = ['BYBIT_API_URL', 'COINBASE_API_URL', 'API_BASE_URL']
            for var in url_vars:
                url = os.getenv(var, '').lower()
                if url:
                    for indicator in testnet_indicators:
                        if indicator in url:
                            issues.append(f"Testnet URL detected in {var}: {url}")
            
            return {
                'valid': len(issues) == 0,
                'issues': issues,
                'warnings': warnings,
                'checked_endpoints': len(url_vars)
            }
            
        except Exception as e:
            return {'valid': False, 'issues': [f"API endpoint validation error: {e}"]}
    
    async def _validate_data_sources(self) -> Dict[str, Any]:
        """Validate that data sources are live and not using fake/cached data"""
        try:
            issues = []
            warnings = []
            
            # This would be expanded to check actual data sources
            # For now, we validate that no hardcoded prices are being used
            
            # Check for hardcoded price patterns in memory (if accessible)
            # This is a placeholder for more comprehensive data source validation
            
            return {
                'valid': True,  # Placeholder - would implement actual data source checks
                'issues': issues,
                'warnings': warnings,
                'data_sources_checked': 0
            }
            
        except Exception as e:
            return {'valid': False, 'issues': [f"Data source validation error: {e}"]}
    
    async def _validate_trading_configuration(self) -> Dict[str, Any]:
        """Validate trading configuration for real money trading"""
        try:
            issues = []
            warnings = []
            
            # Check for simulation-related configuration
            simulation_configs = [
                'use_simulation', 'enable_demo', 'test_mode', 'mock_trading'
            ]
            
            # This would check actual configuration objects if available
            # For now, we perform basic environment checks
            
            return {
                'valid': True,  # Placeholder - would implement actual config validation
                'issues': issues,
                'warnings': warnings,
                'configs_checked': len(simulation_configs)
            }
            
        except Exception as e:
            return {'valid': False, 'issues': [f"Trading configuration validation error: {e}"]}
    
    async def validate_price_data(self, symbol: str, price: float, timestamp: float) -> bool:
        """
        Validate that price data is real-time and not hardcoded/fake
        Returns True if valid, raises exception if fake data detected
        """
        try:
            # Check if price matches prohibited hardcoded patterns
            if price in self.prohibited_patterns['fake_prices']:
                error_msg = f"CRITICAL: Hardcoded/fake price detected for {symbol}: {price}. " \
                           f"Real money trading requires live market data only."
                logger.error(f"❌ [PRICE-VALIDATOR] {error_msg}")
                if self.fail_fast_mode:
                    raise ValueError(error_msg)
                return False
            
            # Check price data age
            current_time = time.time()
            price_age = current_time - timestamp
            if price_age > self.max_price_age_seconds:
                error_msg = f"CRITICAL: Stale price data for {symbol}: {price_age:.1f}s old. " \
                           f"Real money trading requires fresh data (max {self.max_price_age_seconds}s)."
                logger.error(f"❌ [PRICE-VALIDATOR] {error_msg}")
                if self.fail_fast_mode:
                    raise ValueError(error_msg)
                return False
            
            # Price must be positive and reasonable
            if price <= 0:
                error_msg = f"CRITICAL: Invalid price for {symbol}: {price}. " \
                           f"Real money trading requires valid positive prices."
                logger.error(f"❌ [PRICE-VALIDATOR] {error_msg}")
                if self.fail_fast_mode:
                    raise ValueError(error_msg)
                return False
            
            logger.debug(f"✅ [PRICE-VALIDATOR] Price validation passed for {symbol}: {price}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [PRICE-VALIDATOR] Price validation error: {e}")
            if self.fail_fast_mode:
                raise
            return False
    
    async def validate_balance_data(self, exchange: str, currency: str, balance: float) -> bool:
        """
        Validate that balance data is real and not simulated
        Returns True if valid, raises exception if fake data detected
        """
        try:
            # Check for common test balance amounts
            if balance in self.prohibited_patterns['test_balances']:
                error_msg = f"CRITICAL: Test/fake balance detected on {exchange} for {currency}: {balance}. " \
                           f"Real money trading requires actual account balances only."
                logger.error(f"❌ [BALANCE-VALIDATOR] {error_msg}")
                if self.fail_fast_mode:
                    raise ValueError(error_msg)
                return False
            
            # Balance must be non-negative
            if balance < 0:
                error_msg = f"CRITICAL: Invalid negative balance on {exchange} for {currency}: {balance}."
                logger.error(f"❌ [BALANCE-VALIDATOR] {error_msg}")
                if self.fail_fast_mode:
                    raise ValueError(error_msg)
                return False
            
            logger.debug(f"✅ [BALANCE-VALIDATOR] Balance validation passed for {exchange} {currency}: {balance}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [BALANCE-VALIDATOR] Balance validation error: {e}")
            if self.fail_fast_mode:
                raise
            return False
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """Get summary of recent validations"""
        try:
            if not self.validation_history:
                return {'status': 'no_validations', 'message': 'No validations performed yet'}
            
            latest = self.validation_history[-1]
            total_validations = len(self.validation_history)
            successful_validations = sum(1 for v in self.validation_history if v.get('overall_valid', False))
            
            return {
                'status': 'active',
                'total_validations': total_validations,
                'successful_validations': successful_validations,
                'success_rate': (successful_validations / total_validations) * 100,
                'latest_validation': latest,
                'last_validation_time': self.last_validation_time
            }
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}

# Global validator instance
real_money_validator = RealMoneyTradingValidator()
