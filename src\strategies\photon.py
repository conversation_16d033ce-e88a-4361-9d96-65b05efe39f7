from web3 import AsyncWeb3
from eth_account import Account
from cryptography.fernet import Fernet

class PhotonWallet:
    def __init__(self, encrypted_privkey: str, encryption_key: str, rpc_url: str):
        self.cipher = Fernet(encryption_key)
        self.account = Account.from_key(
            self.cipher.decrypt(encrypted_privkey.encode())
        )
        self.w3 = AsyncWeb3(AsyncWeb3.AsyncHTTPProvider(rpc_url))
        
    async def get_balance(self):
        """Get ETH balance"""
        return await self.w3.eth.get_balance(self.account.address)
        
    async def send_transaction(self, tx_params):
        """Execute Ethereum transaction"""
        signed_tx = self.account.sign_transaction(tx_params)
        return await self.w3.eth.send_raw_transaction(signed_tx.rawTransaction)

from jsonschema import Draft7Validator

class ConfigManager:
    def __init__(self):
        self.validator = Draft7Validator(self._load_schema())
        
    def validate(self, config: dict) -> list:
        """Return list of errors instead of raising"""
        return list(self.validator.iter_errors(config))

    def _load_schema(self) -> dict:
        """Load configuration schema"""
        return {
            "type": "object",
            "properties": {
                "trading_enabled": {"type": "boolean"},
                "max_position_size": {"type": "number", "minimum": 0},
                "risk_tolerance": {"type": "number", "minimum": 0, "maximum": 1}
            },
            "required": ["trading_enabled"]
        }