"""
Liquidity Analyzer for Advanced Market Analysis
Professional-grade liquidity analysis for optimal trade execution
"""

import logging
import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class LiquidityLevel(Enum):
    VERY_HIGH = "very_high"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    VERY_LOW = "very_low"

@dataclass
class LiquidityMetrics:
    symbol: str
    bid_depth: Decimal
    ask_depth: Decimal
    spread: Decimal
    liquidity_level: LiquidityLevel
    market_impact: float
    timestamp: float

class LiquidityAnalyzer:
    """Professional liquidity analysis system"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.liquidity_cache = {}
        self.cache_ttl = 30  # 30 seconds
        self.analysis_history = []
        
        logger.info("💧 [LIQUIDITY] Liquidity analyzer initialized")
    
    async def analyze_liquidity(self, symbol: str, exchange: str = "bybit") -> LiquidityMetrics:
        """Analyze liquidity for a trading pair"""
        try:
            logger.info(f"💧 [LIQUIDITY] Analyzing liquidity for {symbol}")
            
            # Check cache first
            cache_key = f"{symbol}_{exchange}"
            if cache_key in self.liquidity_cache:
                cached_data = self.liquidity_cache[cache_key]
                if time.time() - cached_data['timestamp'] < self.cache_ttl:
                    return cached_data['metrics']
            
            # Fetch order book data
            order_book = await self._fetch_order_book(symbol, exchange)
            
            # Calculate liquidity metrics
            metrics = await self._calculate_liquidity_metrics(symbol, order_book)
            
            # Cache results
            self.liquidity_cache[cache_key] = {
                'metrics': metrics,
                'timestamp': time.time()
            }
            
            self.analysis_history.append(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error analyzing liquidity: {e}")
            return LiquidityMetrics(
                symbol=symbol,
                bid_depth=Decimal('0'),
                ask_depth=Decimal('0'),
                spread=Decimal('0'),
                liquidity_level=LiquidityLevel.VERY_LOW,
                market_impact=1.0,
                timestamp=time.time()
            )
    
    async def _fetch_order_book(self, symbol: str, exchange: str) -> Dict[str, Any]:
        """Fetch order book data from exchange"""
        try:
            # Simulate order book fetch
            await asyncio.sleep(0.05)  # Simulate API call
            
            # Mock order book data
            return {
                "bids": [
                    [50000.0, 1.5],
                    [49999.0, 2.0],
                    [49998.0, 1.0]
                ],
                "asks": [
                    [50001.0, 1.2],
                    [50002.0, 1.8],
                    [50003.0, 0.8]
                ]
            }
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error fetching order book: {e}")
            return {"bids": [], "asks": []}
    
    async def _calculate_liquidity_metrics(self, symbol: str, order_book: Dict[str, Any]) -> LiquidityMetrics:
        """Calculate comprehensive liquidity metrics"""
        try:
            bids = order_book.get("bids", [])
            asks = order_book.get("asks", [])
            
            if not bids or not asks:
                return LiquidityMetrics(
                    symbol=symbol,
                    bid_depth=Decimal('0'),
                    ask_depth=Decimal('0'),
                    spread=Decimal('0'),
                    liquidity_level=LiquidityLevel.VERY_LOW,
                    market_impact=1.0,
                    timestamp=time.time()
                )
            
            # Calculate bid/ask depths
            bid_depth = sum(Decimal(str(bid[1])) for bid in bids[:10])  # Top 10 levels
            ask_depth = sum(Decimal(str(ask[1])) for ask in asks[:10])  # Top 10 levels
            
            # Calculate spread
            best_bid = Decimal(str(bids[0][0]))
            best_ask = Decimal(str(asks[0][0]))
            spread = best_ask - best_bid
            
            # Determine liquidity level
            total_depth = bid_depth + ask_depth
            if total_depth > Decimal('100'):
                liquidity_level = LiquidityLevel.VERY_HIGH
                market_impact = 0.1
            elif total_depth > Decimal('50'):
                liquidity_level = LiquidityLevel.HIGH
                market_impact = 0.2
            elif total_depth > Decimal('20'):
                liquidity_level = LiquidityLevel.MEDIUM
                market_impact = 0.4
            elif total_depth > Decimal('5'):
                liquidity_level = LiquidityLevel.LOW
                market_impact = 0.7
            else:
                liquidity_level = LiquidityLevel.VERY_LOW
                market_impact = 1.0
            
            return LiquidityMetrics(
                symbol=symbol,
                bid_depth=bid_depth,
                ask_depth=ask_depth,
                spread=spread,
                liquidity_level=liquidity_level,
                market_impact=market_impact,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error calculating metrics: {e}")
            return LiquidityMetrics(
                symbol=symbol,
                bid_depth=Decimal('0'),
                ask_depth=Decimal('0'),
                spread=Decimal('0'),
                liquidity_level=LiquidityLevel.VERY_LOW,
                market_impact=1.0,
                timestamp=time.time()
            )
    
    async def get_optimal_execution_strategy(self, symbol: str, amount: Decimal, 
                                           side: str) -> Dict[str, Any]:
        """Get optimal execution strategy based on liquidity"""
        try:
            metrics = await self.analyze_liquidity(symbol)
            
            # Determine execution strategy
            if metrics.liquidity_level in [LiquidityLevel.VERY_HIGH, LiquidityLevel.HIGH]:
                strategy = "aggressive"
                execution_time = "immediate"
            elif metrics.liquidity_level == LiquidityLevel.MEDIUM:
                strategy = "balanced"
                execution_time = "5-10 minutes"
            else:
                strategy = "conservative"
                execution_time = "15-30 minutes"
            
            return {
                "strategy": strategy,
                "execution_time": execution_time,
                "liquidity_level": metrics.liquidity_level.value,
                "market_impact": metrics.market_impact,
                "recommended_slippage": metrics.market_impact * 0.5
            }
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error getting execution strategy: {e}")
            return {
                "strategy": "conservative",
                "execution_time": "30+ minutes",
                "liquidity_level": "unknown",
                "market_impact": 1.0,
                "recommended_slippage": 0.5
            }
    
    def get_liquidity_report(self) -> Dict[str, Any]:
        """Get comprehensive liquidity analysis report"""
        return {
            "cached_symbols": len(self.liquidity_cache),
            "analysis_count": len(self.analysis_history),
            "cache_ttl": self.cache_ttl,
            "last_analysis": self.analysis_history[-1] if self.analysis_history else None
        }
