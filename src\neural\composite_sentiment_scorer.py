"""
Composite Sentiment Scoring System
Advanced sentiment analysis combining Fear & Greed Index, web crawler insights, and market data
"""

import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
import asyncio

logger = logging.getLogger(__name__)

@dataclass
class SentimentScore:
    """Comprehensive sentiment score result"""
    composite_score: float  # -1 to 1 scale
    confidence: float      # 0 to 1 scale
    components: Dict[str, float]  # Individual component scores
    signals: Dict[str, Any]       # Trading signals derived from sentiment
    timestamp: datetime
    
class CompositeSentimentScorer:
    """
    Advanced composite sentiment scoring system
    Combines multiple data sources with intelligent weighting and confidence assessment
    """
    
    def __init__(self):
        # Component weights (can be dynamically adjusted)
        self.weights = {
            'fear_greed': 0.35,      # Fear & Greed Index
            'web_sentiment': 0.25,   # Web crawler insights
            'market_sentiment': 0.20, # Market-derived sentiment
            'onchain_sentiment': 0.15, # On-chain sentiment indicators
            'technical_sentiment': 0.05 # Technical analysis sentiment
        }
        
        # Confidence thresholds
        self.confidence_thresholds = {
            'high': 0.8,
            'medium': 0.6,
            'low': 0.4
        }
        
        # Historical sentiment for trend analysis
        self.sentiment_history = []
        self.max_history = 168  # 1 week of hourly data
        
        logger.info("Composite Sentiment Scorer initialized")
    
    async def calculate_composite_sentiment(self, market_data: Dict[str, Any], 
                                          sentiment_data: Optional[Dict[str, Any]] = None) -> SentimentScore:
        """
        Calculate comprehensive composite sentiment score
        
        Args:
            market_data: Market data including Fear & Greed, web insights, on-chain data
            sentiment_data: Additional sentiment analysis data
            
        Returns:
            SentimentScore object with composite score and components
        """
        try:
            # Extract component scores
            components = {}
            
            # 1. Fear & Greed Index sentiment
            components['fear_greed'] = await self._calculate_fear_greed_sentiment(market_data)
            
            # 2. Web crawler sentiment
            components['web_sentiment'] = await self._calculate_web_sentiment(market_data)
            
            # 3. Market-derived sentiment
            components['market_sentiment'] = await self._calculate_market_sentiment(market_data)
            
            # 4. On-chain sentiment
            components['onchain_sentiment'] = await self._calculate_onchain_sentiment(market_data)
            
            # 5. Technical sentiment
            components['technical_sentiment'] = await self._calculate_technical_sentiment(market_data)
            
            # 6. Additional sentiment data
            if sentiment_data:
                components['external_sentiment'] = sentiment_data.get('aggregated_sentiment', 0.0)
                # Adjust weights to include external sentiment
                adjusted_weights = self.weights.copy()
                adjusted_weights['external_sentiment'] = 0.1
                # Normalize weights
                total_weight = sum(adjusted_weights.values())
                adjusted_weights = {k: v/total_weight for k, v in adjusted_weights.items()}
            else:
                adjusted_weights = self.weights
            
            # Calculate weighted composite score
            composite_score = 0.0
            total_weight = 0.0
            
            for component, score in components.items():
                if component in adjusted_weights and score is not None:
                    weight = adjusted_weights[component]
                    composite_score += score * weight
                    total_weight += weight
            
            # Normalize by actual total weight
            if total_weight > 0:
                composite_score = composite_score / total_weight
            
            # Ensure score is within bounds
            composite_score = max(-1.0, min(1.0, composite_score))
            
            # Calculate confidence
            confidence = await self._calculate_confidence(components, market_data)
            
            # Generate trading signals
            signals = await self._generate_sentiment_signals(composite_score, confidence, components)
            
            # Create sentiment score object
            sentiment_score = SentimentScore(
                composite_score=composite_score,
                confidence=confidence,
                components=components,
                signals=signals,
                timestamp=datetime.now(timezone.utc)
            )
            
            # Store in history
            self._update_sentiment_history(sentiment_score)
            
            logger.debug(f"Composite sentiment calculated: score={composite_score:.3f}, "
                        f"confidence={confidence:.3f}")
            
            return sentiment_score
            
        except Exception as e:
            logger.error(f"Error calculating composite sentiment: {e}")
            return self._get_default_sentiment_score()
    
    async def _calculate_fear_greed_sentiment(self, market_data: Dict[str, Any]) -> float:
        """Calculate sentiment from Fear & Greed Index"""
        try:
            live_aggregated = market_data.get('live_aggregated', {})
            fear_greed_index = live_aggregated.get('fear_greed_index', 65.0)
            
            # Convert Fear & Greed Index to sentiment score
            # 0-25: Extreme Fear (-0.8 to -0.6)
            # 25-45: Fear (-0.6 to -0.2)
            # 45-55: Neutral (-0.2 to 0.2)
            # 55-75: Greed (0.2 to 0.6)
            # 75-100: Extreme Greed (0.6 to 0.8)
            
            if fear_greed_index <= 25:
                sentiment = -0.8 + (fear_greed_index / 25.0) * 0.2
            elif fear_greed_index <= 45:
                sentiment = -0.6 + ((fear_greed_index - 25) / 20.0) * 0.4
            elif fear_greed_index <= 55:
                sentiment = -0.2 + ((fear_greed_index - 45) / 10.0) * 0.4
            elif fear_greed_index <= 75:
                sentiment = 0.2 + ((fear_greed_index - 55) / 20.0) * 0.4
            else:
                sentiment = 0.6 + ((fear_greed_index - 75) / 25.0) * 0.2
            
            return sentiment
            
        except Exception as e:
            logger.error(f"Error calculating Fear & Greed sentiment: {e}")
            return 0.0
    
    async def _calculate_web_sentiment(self, market_data: Dict[str, Any]) -> float:
        """Calculate sentiment from web crawler insights"""
        try:
            web_insights = market_data.get('web_crawler_insights', {})
            
            if not web_insights:
                return 0.0
            
            # Base web sentiment
            overall_sentiment = web_insights.get('overall_sentiment', 0.0)
            confidence = web_insights.get('confidence', 0.0)
            high_impact_count = web_insights.get('high_impact_count', 0)
            
            # Adjust sentiment based on news impact
            impact_adjustment = min(0.2, high_impact_count * 0.05)
            if overall_sentiment > 0:
                adjusted_sentiment = overall_sentiment + impact_adjustment
            else:
                adjusted_sentiment = overall_sentiment - impact_adjustment
            
            # Weight by confidence
            weighted_sentiment = adjusted_sentiment * confidence
            
            return max(-1.0, min(1.0, weighted_sentiment))
            
        except Exception as e:
            logger.error(f"Error calculating web sentiment: {e}")
            return 0.0
    
    async def _calculate_market_sentiment(self, market_data: Dict[str, Any]) -> float:
        """Calculate sentiment from market price action and volume"""
        try:
            price_data = market_data.get('price_data', {})
            
            if not price_data:
                return 0.0
            
            # Aggregate market metrics
            price_changes_24h = []
            volume_changes = []
            volatilities = []
            
            for symbol_data in price_data.values():
                if 'price_change_24h' in symbol_data:
                    price_changes_24h.append(float(symbol_data['price_change_24h']))
                if 'volume_change_24h' in symbol_data:
                    volume_changes.append(float(symbol_data['volume_change_24h']))
                if 'volatility' in symbol_data:
                    volatilities.append(float(symbol_data['volatility']))
            
            if not price_changes_24h:
                return 0.0
            
            # Calculate market sentiment components
            avg_price_change = np.mean(price_changes_24h)
            avg_volume_change = np.mean(volume_changes) if volume_changes else 0.0
            avg_volatility = np.mean(volatilities) if volatilities else 0.1
            
            # Price momentum sentiment
            price_sentiment = max(-1.0, min(1.0, avg_price_change / 10.0))
            
            # Volume confirmation
            volume_confirmation = 1.0 if avg_volume_change > 0 else 0.8
            
            # Volatility adjustment (high volatility reduces confidence)
            volatility_adjustment = max(0.5, 1.0 - (avg_volatility / 0.2))
            
            # Combined market sentiment
            market_sentiment = price_sentiment * volume_confirmation * volatility_adjustment
            
            return market_sentiment
            
        except Exception as e:
            logger.error(f"Error calculating market sentiment: {e}")
            return 0.0
    
    async def _calculate_onchain_sentiment(self, market_data: Dict[str, Any]) -> float:
        """Calculate sentiment from on-chain indicators"""
        try:
            live_aggregated = market_data.get('live_aggregated', {})
            
            # Bitcoin on-chain sentiment
            btc_data = live_aggregated.get('bitcoin_data', {})
            btc_sentiment = 0.0
            
            if btc_data:
                # Hash rate growth = positive sentiment
                hash_rate_norm = btc_data.get('hash_rate', 300e18) / 400e18
                btc_sentiment += (hash_rate_norm - 0.75) * 0.5
                
                # HODL waves = long-term confidence
                hodl_waves = btc_data.get('hodl_waves', 0.6)
                btc_sentiment += (hodl_waves - 0.5) * 0.3
                
                # Exchange flows (negative = accumulation)
                exchange_flows = btc_data.get('exchange_flows', 0.0)
                btc_sentiment -= exchange_flows * 0.2
            
            # Ethereum on-chain sentiment
            eth_data = live_aggregated.get('ethereum_data', {})
            eth_sentiment = 0.0
            
            if eth_data:
                # Network utilization
                network_util = eth_data.get('network_utilization', 0.5)
                eth_sentiment += (network_util - 0.5) * 0.3
                
                # DeFi TVL growth
                defi_tvl_change = eth_data.get('defi_tvl_change', 0.0)
                eth_sentiment += defi_tvl_change * 0.4
                
                # Whale activity (moderate is good)
                whale_activity = eth_data.get('whale_activity', 0.0)
                if 0.2 <= whale_activity <= 0.6:
                    eth_sentiment += 0.2
                elif whale_activity > 0.8:
                    eth_sentiment -= 0.3
            
            # DeFi sentiment
            defi_data = live_aggregated.get('defi_data', {})
            defi_sentiment = 0.0
            
            if defi_data:
                # TVL growth
                tvl_norm = defi_data.get('total_value_locked', 50e9) / 100e9
                defi_sentiment += (tvl_norm - 0.5) * 0.3
                
                # Yield opportunities
                avg_yield = defi_data.get('yield_farming_apy', 0.05)
                if 0.03 <= avg_yield <= 0.15:  # Healthy yield range
                    defi_sentiment += 0.2
                elif avg_yield > 0.25:  # Unsustainable yields
                    defi_sentiment -= 0.3
                
                # Liquidation risk
                liquidation_risk = defi_data.get('liquidation_risk', 0.1)
                defi_sentiment -= liquidation_risk * 0.5
            
            # Combine on-chain sentiments
            onchain_sentiment = (btc_sentiment * 0.4 + eth_sentiment * 0.4 + defi_sentiment * 0.2)
            
            return max(-1.0, min(1.0, onchain_sentiment))
            
        except Exception as e:
            logger.error(f"Error calculating on-chain sentiment: {e}")
            return 0.0
    
    async def _calculate_technical_sentiment(self, market_data: Dict[str, Any]) -> float:
        """Calculate sentiment from technical indicators"""
        try:
            technical_data = market_data.get('technical_indicators', {})
            
            if not technical_data:
                return 0.0
            
            sentiment = 0.0
            
            # RSI sentiment
            rsi = technical_data.get('rsi', 50.0)
            if rsi < 30:
                sentiment += 0.3  # Oversold = bullish
            elif rsi > 70:
                sentiment -= 0.3  # Overbought = bearish
            
            # MACD sentiment
            macd = technical_data.get('macd', 0.0)
            macd_signal = technical_data.get('macd_signal', 0.0)
            if macd > macd_signal:
                sentiment += 0.2  # Bullish crossover
            else:
                sentiment -= 0.2  # Bearish crossover
            
            # Bollinger Bands sentiment
            bollinger_position = technical_data.get('bollinger_position', 0.5)
            if bollinger_position < 0.2:
                sentiment += 0.2  # Near lower band = oversold
            elif bollinger_position > 0.8:
                sentiment -= 0.2  # Near upper band = overbought
            
            return max(-1.0, min(1.0, sentiment))
            
        except Exception as e:
            logger.error(f"Error calculating technical sentiment: {e}")
            return 0.0

    async def _calculate_confidence(self, components: Dict[str, float], market_data: Dict[str, Any]) -> float:
        """Calculate confidence in the composite sentiment score"""
        try:
            confidence_factors = []

            # Data availability confidence
            available_components = sum(1 for score in components.values() if score is not None)
            data_confidence = available_components / len(self.weights)
            confidence_factors.append(data_confidence)

            # Component agreement confidence
            non_null_scores = [score for score in components.values() if score is not None]
            if len(non_null_scores) >= 2:
                score_std = np.std(non_null_scores)
                agreement_confidence = max(0.0, 1.0 - score_std)  # Lower std = higher agreement
                confidence_factors.append(agreement_confidence)

            # Market volatility confidence (lower volatility = higher confidence)
            price_data = market_data.get('price_data', {})
            if price_data:
                volatilities = [
                    float(data.get('volatility', 0.1))
                    for data in price_data.values()
                    if 'volatility' in data
                ]
                if volatilities:
                    avg_volatility = np.mean(volatilities)
                    volatility_confidence = max(0.3, 1.0 - (avg_volatility / 0.2))
                    confidence_factors.append(volatility_confidence)

            # Historical consistency confidence
            if len(self.sentiment_history) >= 3:
                recent_scores = [entry.composite_score for entry in self.sentiment_history[-3:]]
                consistency = 1.0 - np.std(recent_scores)
                consistency_confidence = max(0.0, consistency)
                confidence_factors.append(consistency_confidence)

            # Calculate overall confidence
            if confidence_factors:
                overall_confidence = np.mean(confidence_factors)
            else:
                overall_confidence = 0.5

            return max(0.0, min(1.0, overall_confidence))

        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.5

    async def _generate_sentiment_signals(self, composite_score: float, confidence: float,
                                        components: Dict[str, float]) -> Dict[str, Any]:
        """Generate trading signals based on sentiment analysis"""
        try:
            signals = {}

            # Primary sentiment signal
            if confidence >= self.confidence_thresholds['high']:
                if composite_score >= 0.6:
                    signals['primary_signal'] = 'STRONG_BUY'
                elif composite_score >= 0.3:
                    signals['primary_signal'] = 'BUY'
                elif composite_score <= -0.6:
                    signals['primary_signal'] = 'STRONG_SELL'
                elif composite_score <= -0.3:
                    signals['primary_signal'] = 'SELL'
                else:
                    signals['primary_signal'] = 'NEUTRAL'
            elif confidence >= self.confidence_thresholds['medium']:
                if composite_score >= 0.4:
                    signals['primary_signal'] = 'WEAK_BUY'
                elif composite_score <= -0.4:
                    signals['primary_signal'] = 'WEAK_SELL'
                else:
                    signals['primary_signal'] = 'NEUTRAL'
            else:
                signals['primary_signal'] = 'NEUTRAL'

            # Contrarian signals (for extreme sentiment)
            if composite_score >= 0.8 and confidence >= 0.7:
                signals['contrarian_signal'] = 'CONSIDER_SELL'  # Extreme optimism
            elif composite_score <= -0.8 and confidence >= 0.7:
                signals['contrarian_signal'] = 'CONSIDER_BUY'   # Extreme pessimism
            else:
                signals['contrarian_signal'] = 'NONE'

            # Component-specific signals
            fear_greed_score = components.get('fear_greed', 0.0)
            if fear_greed_score <= -0.7:
                signals['fear_greed_signal'] = 'EXTREME_FEAR_BUY_OPPORTUNITY'
            elif fear_greed_score >= 0.7:
                signals['fear_greed_signal'] = 'EXTREME_GREED_SELL_WARNING'
            else:
                signals['fear_greed_signal'] = 'NORMAL'

            # Trend signals
            if len(self.sentiment_history) >= 5:
                recent_scores = [entry.composite_score for entry in self.sentiment_history[-5:]]
                trend = np.polyfit(range(len(recent_scores)), recent_scores, 1)[0]

                if trend > 0.1:
                    signals['trend_signal'] = 'IMPROVING_SENTIMENT'
                elif trend < -0.1:
                    signals['trend_signal'] = 'DETERIORATING_SENTIMENT'
                else:
                    signals['trend_signal'] = 'STABLE_SENTIMENT'
            else:
                signals['trend_signal'] = 'INSUFFICIENT_DATA'

            # Risk assessment
            if confidence < self.confidence_thresholds['low']:
                signals['risk_warning'] = 'LOW_CONFIDENCE_DATA'
            elif abs(composite_score) > 0.8:
                signals['risk_warning'] = 'EXTREME_SENTIMENT_RISK'
            else:
                signals['risk_warning'] = 'NORMAL_RISK'

            # Timing signals
            signals['optimal_entry_confidence'] = confidence
            signals['sentiment_strength'] = abs(composite_score)

            return signals

        except Exception as e:
            logger.error(f"Error generating sentiment signals: {e}")
            return {'primary_signal': 'NEUTRAL', 'error': str(e)}

    def _update_sentiment_history(self, sentiment_score: SentimentScore):
        """Update sentiment history for trend analysis"""
        try:
            self.sentiment_history.append(sentiment_score)

            # Keep only recent history
            if len(self.sentiment_history) > self.max_history:
                self.sentiment_history = self.sentiment_history[-self.max_history:]

        except Exception as e:
            logger.error(f"Error updating sentiment history: {e}")

    def _get_default_sentiment_score(self) -> SentimentScore:
        """Get default sentiment score for error cases"""
        return SentimentScore(
            composite_score=0.0,
            confidence=0.5,
            components={
                'fear_greed': 0.0,
                'web_sentiment': 0.0,
                'market_sentiment': 0.0,
                'onchain_sentiment': 0.0,
                'technical_sentiment': 0.0
            },
            signals={'primary_signal': 'NEUTRAL', 'error': 'Default values used'},
            timestamp=datetime.now(timezone.utc)
        )

    def get_sentiment_summary(self) -> Dict[str, Any]:
        """Get summary of recent sentiment analysis"""
        try:
            if not self.sentiment_history:
                return {'status': 'No sentiment history available'}

            recent_scores = [entry.composite_score for entry in self.sentiment_history[-24:]]  # Last 24 hours
            recent_confidences = [entry.confidence for entry in self.sentiment_history[-24:]]

            return {
                'current_sentiment': self.sentiment_history[-1].composite_score,
                'current_confidence': self.sentiment_history[-1].confidence,
                'avg_sentiment_24h': np.mean(recent_scores),
                'sentiment_volatility_24h': np.std(recent_scores),
                'avg_confidence_24h': np.mean(recent_confidences),
                'sentiment_trend': 'improving' if recent_scores[-1] > recent_scores[0] else 'deteriorating',
                'data_points': len(self.sentiment_history),
                'last_update': self.sentiment_history[-1].timestamp.isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting sentiment summary: {e}")
            return {'error': str(e)}

    def adjust_weights(self, new_weights: Dict[str, float]):
        """Dynamically adjust component weights"""
        try:
            # Validate weights
            if not all(0 <= weight <= 1 for weight in new_weights.values()):
                raise ValueError("All weights must be between 0 and 1")

            # Normalize weights to sum to 1
            total_weight = sum(new_weights.values())
            if total_weight > 0:
                normalized_weights = {k: v/total_weight for k, v in new_weights.items()}
                self.weights.update(normalized_weights)
                logger.info(f"Sentiment weights updated: {self.weights}")
            else:
                logger.warning("Invalid weights provided - sum is zero")

        except Exception as e:
            logger.error(f"Error adjusting weights: {e}")

# Export the main classes
__all__ = ['CompositeSentimentScorer', 'SentimentScore']
