#!/usr/bin/env python3
"""
Data Source Auditor - Comprehensive Real Money Trading Validation
Scans entire codebase for non-live data sources and enforces real-money trading only
"""

import os
import re
import ast
import json
import logging
import asyncio
from pathlib import Path
from typing import Dict, List, Set, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class DataSourceViolation:
    """Represents a violation of real-money trading requirements"""
    file_path: str
    line_number: int
    violation_type: str
    description: str
    code_snippet: str
    severity: str  # CRITICAL, HIGH, MEDIUM, LOW
    suggested_fix: str

@dataclass
class AuditResult:
    """Results of data source audit"""
    total_files_scanned: int
    violations_found: List[DataSourceViolation]
    critical_violations: int
    high_violations: int
    medium_violations: int
    low_violations: int
    is_compliant: bool
    audit_timestamp: datetime

class DataSourceAuditor:
    """Comprehensive auditor for real-money trading compliance"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.violations: List[DataSourceViolation] = []
        
        # Patterns that indicate non-live data usage
        self.violation_patterns = {
            'mock_data': {
                'patterns': [
                    r'mock[_\s]*data',
                    r'fake[_\s]*data',
                    r'dummy[_\s]*data',
                    r'test[_\s]*data',
                    r'sample[_\s]*data',
                    r'placeholder[_\s]*data',
                    r'synthetic[_\s]*data',
                    r'simulated[_\s]*data',
                    r'hardcoded[_\s]*data',
                    r'fallback[_\s]*data'
                ],
                'severity': 'CRITICAL',
                'description': 'Mock/fake data usage detected'
            },
            'hardcoded_prices': {
                'patterns': [
                    r'["\']BTCUSDT["\']:\s*\d+\.?\d*',
                    r'["\']ETHUSDT["\']:\s*\d+\.?\d*',
                    r'["\']price["\']:\s*\d+\.?\d*',
                    r'default_price\s*=\s*\d+',
                    r'fallback_price\s*=\s*\d+',
                    r'mock_price\s*=\s*\d+'
                ],
                'severity': 'CRITICAL',
                'description': 'Hardcoded price values detected'
            },
            'simulation_modes': {
                'patterns': [
                    r'simulation[_\s]*mode',
                    r'test[_\s]*mode',
                    r'demo[_\s]*mode',
                    r'mock[_\s]*mode',
                    r'sandbox[_\s]*mode',
                    r'paper[_\s]*trading',
                    r'virtual[_\s]*trading',
                    r'fake[_\s]*trading',
                    r'dry[_\s]*run'
                ],
                'severity': 'CRITICAL',
                'description': 'Simulation/test mode detected'
            },
            'fallback_mechanisms': {
                'patterns': [
                    r'fallback[_\s]*to',
                    r'emergency[_\s]*fallback',
                    r'default[_\s]*fallback',
                    r'backup[_\s]*data',
                    r'cached[_\s]*data',
                    r'stale[_\s]*data',
                    r'offline[_\s]*data'
                ],
                'severity': 'HIGH',
                'description': 'Fallback mechanism detected'
            },
            'test_endpoints': {
                'patterns': [
                    r'testnet\.bybit\.com',
                    r'sandbox\.coinbase\.com',
                    r'api\.sandbox\.',
                    r'test\.api\.',
                    r'demo\.api\.',
                    r'staging\.api\.'
                ],
                'severity': 'CRITICAL',
                'description': 'Test/sandbox API endpoint detected'
            },
            'mock_classes': {
                'patterns': [
                    r'class\s+Mock\w+',
                    r'class\s+Fake\w+',
                    r'class\s+Test\w+',
                    r'class\s+Dummy\w+',
                    r'MockClient',
                    r'FakeClient',
                    r'TestClient'
                ],
                'severity': 'HIGH',
                'description': 'Mock/test class detected'
            }
        }
        
        # File patterns to scan
        self.file_patterns = [
            '*.py',
            '*.json',
            '*.yaml',
            '*.yml',
            '*.env',
            '*.cfg',
            '*.ini'
        ]
        
        # Directories to exclude from scanning
        self.exclude_dirs = {
            '__pycache__',
            '.git',
            '.pytest_cache',
            'node_modules',
            '.venv',
            'venv',
            'env'
        }

    async def audit_codebase(self) -> AuditResult:
        """Perform comprehensive audit of codebase for real-money trading compliance"""
        logger.info("🔍 [AUDIT] Starting comprehensive data source audit...")
        
        start_time = datetime.now()
        files_scanned = 0
        
        try:
            # Scan all relevant files
            for file_path in self._get_files_to_scan():
                try:
                    await self._audit_file(file_path)
                    files_scanned += 1
                    
                    if files_scanned % 50 == 0:
                        logger.info(f"🔍 [AUDIT] Scanned {files_scanned} files...")
                        
                except Exception as e:
                    logger.warning(f"⚠️ [AUDIT] Error scanning {file_path}: {e}")
                    continue
            
            # Categorize violations by severity
            critical_count = len([v for v in self.violations if v.severity == 'CRITICAL'])
            high_count = len([v for v in self.violations if v.severity == 'HIGH'])
            medium_count = len([v for v in self.violations if v.severity == 'MEDIUM'])
            low_count = len([v for v in self.violations if v.severity == 'LOW'])
            
            # System is compliant only if no critical or high violations
            is_compliant = critical_count == 0 and high_count == 0
            
            audit_result = AuditResult(
                total_files_scanned=files_scanned,
                violations_found=self.violations,
                critical_violations=critical_count,
                high_violations=high_count,
                medium_violations=medium_count,
                low_violations=low_count,
                is_compliant=is_compliant,
                audit_timestamp=datetime.now()
            )
            
            # Log audit summary
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"🔍 [AUDIT] Completed in {duration:.2f}s")
            logger.info(f"🔍 [AUDIT] Files scanned: {files_scanned}")
            logger.info(f"🔍 [AUDIT] Total violations: {len(self.violations)}")
            logger.info(f"🔍 [AUDIT] Critical: {critical_count}, High: {high_count}, Medium: {medium_count}, Low: {low_count}")
            
            if not is_compliant:
                logger.error(f"❌ [AUDIT] SYSTEM NOT COMPLIANT - {critical_count + high_count} critical/high violations found")
            else:
                logger.info("✅ [AUDIT] System is compliant with real-money trading requirements")
            
            return audit_result
            
        except Exception as e:
            logger.error(f"❌ [AUDIT] Audit failed: {e}")
            raise RuntimeError(f"Data source audit failed: {e}")

    def _get_files_to_scan(self) -> List[Path]:
        """Get list of files to scan for violations"""
        files_to_scan = []
        
        for pattern in self.file_patterns:
            for file_path in self.project_root.rglob(pattern):
                # Skip excluded directories
                if any(exclude_dir in file_path.parts for exclude_dir in self.exclude_dirs):
                    continue
                
                # Skip binary files and very large files
                if file_path.is_file() and file_path.stat().st_size < 10 * 1024 * 1024:  # 10MB limit
                    files_to_scan.append(file_path)
        
        return files_to_scan

    async def _audit_file(self, file_path: Path):
        """Audit a single file for violations"""
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # Check each line for violations
            for line_num, line in enumerate(lines, 1):
                await self._check_line_for_violations(file_path, line_num, line)
            
            # Additional checks for Python files
            if file_path.suffix == '.py':
                await self._audit_python_file(file_path, content)
            
            # Additional checks for config files
            if file_path.suffix in ['.json', '.yaml', '.yml']:
                await self._audit_config_file(file_path, content)
                
        except Exception as e:
            logger.debug(f"Error auditing {file_path}: {e}")

    async def _check_line_for_violations(self, file_path: Path, line_num: int, line: str):
        """Check a single line for violations"""
        line_lower = line.lower().strip()
        
        # Skip comments and empty lines
        if not line_lower or line_lower.startswith('#') or line_lower.startswith('//'):
            return
        
        for violation_type, config in self.violation_patterns.items():
            for pattern in config['patterns']:
                if re.search(pattern, line_lower, re.IGNORECASE):
                    violation = DataSourceViolation(
                        file_path=str(file_path.relative_to(self.project_root)),
                        line_number=line_num,
                        violation_type=violation_type,
                        description=config['description'],
                        code_snippet=line.strip(),
                        severity=config['severity'],
                        suggested_fix=self._get_suggested_fix(violation_type, line)
                    )
                    self.violations.append(violation)

    async def _audit_python_file(self, file_path: Path, content: str):
        """Perform additional Python-specific audits"""
        try:
            # Parse AST for deeper analysis
            tree = ast.parse(content)
            
            # Check for mock imports
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if any(keyword in alias.name.lower() for keyword in ['mock', 'fake', 'test', 'dummy']):
                            violation = DataSourceViolation(
                                file_path=str(file_path.relative_to(self.project_root)),
                                line_number=node.lineno,
                                violation_type='mock_imports',
                                description='Mock/test import detected',
                                code_snippet=f"import {alias.name}",
                                severity='HIGH',
                                suggested_fix='Remove mock imports and use real implementations'
                            )
                            self.violations.append(violation)
                            
        except SyntaxError:
            # Skip files with syntax errors
            pass
        except Exception as e:
            logger.debug(f"Error parsing Python file {file_path}: {e}")

    async def _audit_config_file(self, file_path: Path, content: str):
        """Audit configuration files for violations"""
        try:
            if file_path.suffix == '.json':
                config = json.loads(content)
                await self._check_config_values(file_path, config)
        except json.JSONDecodeError:
            pass
        except Exception as e:
            logger.debug(f"Error auditing config file {file_path}: {e}")

    async def _check_config_values(self, file_path: Path, config: dict, path: str = ""):
        """Recursively check configuration values"""
        if isinstance(config, dict):
            for key, value in config.items():
                current_path = f"{path}.{key}" if path else key
                
                # Check for simulation mode configurations
                if any(keyword in key.lower() for keyword in ['test', 'demo', 'mock', 'simulation', 'sandbox']):
                    if value in [True, 'true', '1', 'yes', 'on']:
                        violation = DataSourceViolation(
                            file_path=str(file_path.relative_to(self.project_root)),
                            line_number=1,
                            violation_type='config_simulation_mode',
                            description=f'Simulation mode enabled in config: {current_path}',
                            code_snippet=f'"{key}": {value}',
                            severity='CRITICAL',
                            suggested_fix=f'Set {key} to false or remove it'
                        )
                        self.violations.append(violation)
                
                # Recursively check nested values
                if isinstance(value, dict):
                    await self._check_config_values(file_path, value, current_path)

    def _get_suggested_fix(self, violation_type: str, line: str) -> str:
        """Get suggested fix for a violation"""
        fixes = {
            'mock_data': 'Replace with real-time API calls to live exchanges',
            'hardcoded_prices': 'Remove hardcoded prices and fetch from live exchange APIs',
            'simulation_modes': 'Disable simulation mode and enable live trading only',
            'fallback_mechanisms': 'Remove fallback and implement fail-fast behavior',
            'test_endpoints': 'Replace with production API endpoints',
            'mock_classes': 'Replace mock classes with real exchange client implementations'
        }
        return fixes.get(violation_type, 'Review and fix to ensure real-money trading compliance')

    def generate_audit_report(self, audit_result: AuditResult) -> str:
        """Generate detailed audit report"""
        report = []
        report.append("=" * 80)
        report.append("REAL MONEY TRADING COMPLIANCE AUDIT REPORT")
        report.append("=" * 80)
        report.append(f"Audit Timestamp: {audit_result.audit_timestamp}")
        report.append(f"Files Scanned: {audit_result.total_files_scanned}")
        report.append(f"Total Violations: {len(audit_result.violations_found)}")
        report.append(f"Critical: {audit_result.critical_violations}")
        report.append(f"High: {audit_result.high_violations}")
        report.append(f"Medium: {audit_result.medium_violations}")
        report.append(f"Low: {audit_result.low_violations}")
        report.append(f"Compliance Status: {'✅ COMPLIANT' if audit_result.is_compliant else '❌ NON-COMPLIANT'}")
        report.append("")
        
        if audit_result.violations_found:
            report.append("VIOLATIONS FOUND:")
            report.append("-" * 40)
            
            # Group violations by severity
            for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
                severity_violations = [v for v in audit_result.violations_found if v.severity == severity]
                if severity_violations:
                    report.append(f"\n{severity} VIOLATIONS ({len(severity_violations)}):")
                    for violation in severity_violations:
                        report.append(f"  File: {violation.file_path}:{violation.line_number}")
                        report.append(f"  Type: {violation.violation_type}")
                        report.append(f"  Description: {violation.description}")
                        report.append(f"  Code: {violation.code_snippet}")
                        report.append(f"  Fix: {violation.suggested_fix}")
                        report.append("")
        
        return "\n".join(report)

# Global auditor instance
data_source_auditor = DataSourceAuditor()
