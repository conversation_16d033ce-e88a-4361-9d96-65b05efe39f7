# backend/src/strategies/base.py
from dataclasses import dataclass
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod
import logging

logger = logging.getLogger("strategies.base")

@dataclass
class TradeSignal:
    """Container for trading signals with metadata"""
    symbol: str
    action: str  # "BUY", "SELL", or "HOLD"
    amount: float
    confidence: float = 1.0
    indicators: Dict[str, Any] = None
    metadata: Dict[str, Any] = None

    def validate(self) -> bool:
        """Validate signal parameters"""
        valid_actions = {"BUY", "SELL", "HOLD"}
        return (
            self.action in valid_actions and
            0 < self.amount <= 1 and
            0 <= self.confidence <= 1
        )

class BaseStrategy(ABC):
    """Abstract base class for all trading strategies"""

    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.is_live_trading = True  # Force live trading
        self.last_signal_time = None
        self.signal_history: List[SignalData] = []
        self.circuit_breaker = False
        self.min_data_points = 100
        self.performance_metrics = {
            'total_signals': 0,
            'successful_signals': 0,
            'profit_loss': Decimal('0'),
            'win_rate': 0.0,
            'avg_hold_time': 0.0,
            'sharpe_ratio': 0.0
        }
        
        # OPTIMIZED: Strategy parameters for higher frequency trading
        self.min_confidence_threshold = self.config.get('min_confidence', 0.45)  # Reduced from 0.6 to 0.45
        self.max_position_size = self.config.get('max_position_size', Decimal('0.1'))  # 10% of portfolio
        self.stop_loss_pct = self.config.get('stop_loss_pct', Decimal('0.02'))  # 2%
        self.take_profit_pct = self.config.get('take_profit_pct', Decimal('0.04'))  # 4%
        self.cooldown_minutes = self.config.get('cooldown_minutes', 1)  # Reduced from 5 to 1 minute

        # Remove duplicate warning - main.py handles single warning
        logger.info(f"{name} strategy initialized for REAL TRADING")

    @abstractmethod
    async def analyze_market(self, market_data: Dict) -> List[TradeSignal]:
        """Main analysis method to be implemented by subclasses"""
        pass

    def apply_risk_management(self, signals: List[TradeSignal]) -> List[TradeSignal]:
        """Apply risk management rules to generated signals"""
        return [
            signal for signal in signals 
            if signal.validate() and not self.circuit_breaker
        ]

    def _trigger_circuit_breaker(self):
        """Activate safety mechanism to halt trading"""
        self.circuit_breaker = True
        self.logger.critical("Circuit breaker triggered - trading halted")
        
    def reset_circuit_breaker(self):
        """Reset the circuit breaker after manual review"""
        self.circuit_breaker = False
        self.logger.info("Circuit breaker reset - trading enabled")

    @staticmethod
    def normalize_data(data: Dict) -> Dict:
        """Standardize market data format across exchanges"""
        return {
            "timestamp": data.get("timestamp"),
            "open": float(data.get("open", 0)),
            "high": float(data.get("high", 0)),
            "low": float(data.get("low", 0)),
            "close": float(data.get("close", 0)),
            "volume": float(data.get("volume", 0)),
            "symbol": data.get("symbol")
        }