"""
Performance Optimization and GPU Acceleration for Neural Trading Models
Optimizes models for real-time trading with sub-second inference times
"""

import torch
import torch.nn as nn
import torch.jit as jit
import torch.quantization as quantization
import numpy as np
import time
import logging
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass
from pathlib import Path
import threading
from concurrent.futures import ThreadPoolExecutor
import queue

# Optional psutil import with fallback
try:
    import psutil
except ImportError:
    # Create mock psutil for fallback
    class MockPsutil:
        class Process:
            def memory_info(self):
                class MemInfo:
                    rss = 100 * 1024 * 1024  # 100MB default
                return MemInfo()
        def cpu_percent(self):
            return 50.0  # Default 50% CPU
    psutil = MockPsutil()

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics for model optimization"""
    inference_time_ms: float = 0.0
    throughput_samples_per_sec: float = 0.0
    memory_usage_mb: float = 0.0
    gpu_utilization_percent: float = 0.0
    cpu_utilization_percent: float = 0.0
    model_size_mb: float = 0.0
    accuracy_retention: float = 1.0

@dataclass
class OptimizationConfig:
    """Configuration for model optimization"""
    target_inference_time_ms: float = 100.0  # Sub-second target
    enable_gpu_acceleration: bool = True
    enable_quantization: bool = True
    enable_jit_compilation: bool = True
    enable_tensorrt: bool = False
    batch_size_optimization: bool = True
    memory_optimization: bool = True
    precision: str = 'fp16'  # 'fp32', 'fp16', 'int8'

class ModelCompressor:
    """Model compression techniques for faster inference"""
    
    def __init__(self):
        self.compression_methods = {
            'quantization': self._apply_quantization,
            'pruning': self._apply_pruning,
            'knowledge_distillation': self._apply_distillation,
            'weight_sharing': self._apply_weight_sharing
        }
    
    def compress_model(self, model: nn.Module, method: str = 'quantization',
                      compression_ratio: float = 0.5) -> nn.Module:
        """Compress model using specified method"""
        if method not in self.compression_methods:
            raise ValueError(f"Unknown compression method: {method}")
        
        logger.info(f"🗜️ [COMPRESS] Applying {method} compression...")
        compressed_model = self.compression_methods[method](model, compression_ratio)
        
        # Measure compression effectiveness
        original_size = self._get_model_size(model)
        compressed_size = self._get_model_size(compressed_model)
        compression_achieved = (original_size - compressed_size) / original_size
        
        logger.info(f"✅ [COMPRESS] Compression achieved: {compression_achieved:.2%}")
        return compressed_model
    
    def _apply_quantization(self, model: nn.Module, ratio: float) -> nn.Module:
        """Apply dynamic quantization"""
        model.eval()
        
        # Prepare model for quantization
        model_prepared = quantization.prepare(model, inplace=False)
        
        # Apply dynamic quantization
        quantized_model = quantization.convert(model_prepared, inplace=False)
        
        return quantized_model
    
    def _apply_pruning(self, model: nn.Module, ratio: float) -> nn.Module:
        """Apply structured pruning"""
        import torch.nn.utils.prune as prune
        
        # Apply pruning to linear layers
        for name, module in model.named_modules():
            if isinstance(module, nn.Linear):
                prune.l1_unstructured(module, name='weight', amount=ratio)
                prune.remove(module, 'weight')
        
        return model
    
    def _apply_distillation(self, model: nn.Module, ratio: float) -> nn.Module:
        """Apply knowledge distillation (simplified)"""
        # Create smaller student model
        student_model = self._create_student_model(model, ratio)
        
        # In practice, this would involve training the student model
        # with the teacher model's outputs
        logger.info("Knowledge distillation would require training phase")
        
        return student_model
    
    def _apply_weight_sharing(self, model: nn.Module, ratio: float) -> nn.Module:
        """Apply weight sharing compression"""
        # Simplified weight sharing implementation
        for module in model.modules():
            if isinstance(module, nn.Linear):
                weight = module.weight.data
                # Cluster weights and share values
                unique_weights = torch.unique(weight)
                num_clusters = int(len(unique_weights) * (1 - ratio))
                
                if num_clusters > 1:
                    # Simple clustering by quantiles
                    quantiles = torch.quantile(unique_weights, 
                                             torch.linspace(0, 1, num_clusters))
                    
                    # Replace weights with nearest cluster center
                    for i, q in enumerate(quantiles):
                        mask = torch.abs(weight - q) == torch.min(
                            torch.abs(weight.unsqueeze(-1) - quantiles), dim=-1)[0]
                        weight[mask] = q
        
        return model
    
    def _create_student_model(self, teacher_model: nn.Module, ratio: float) -> nn.Module:
        """Create smaller student model"""
        # Simplified student model creation
        # In practice, this would analyze teacher architecture
        return teacher_model  # Placeholder
    
    def _get_model_size(self, model: nn.Module) -> float:
        """Get model size in MB"""
        param_size = 0
        for param in model.parameters():
            param_size += param.nelement() * param.element_size()
        
        buffer_size = 0
        for buffer in model.buffers():
            buffer_size += buffer.nelement() * buffer.element_size()
        
        return (param_size + buffer_size) / (1024 * 1024)

class GPUAccelerator:
    """GPU acceleration and optimization"""
    
    def __init__(self):
        self.device = self._get_best_device()
        self.cuda_available = torch.cuda.is_available()
        self.mixed_precision = self.cuda_available
        
        if self.cuda_available:
            logger.info(f"🚀 [GPU] Using device: {self.device}")
            logger.info(f"🚀 [GPU] GPU: {torch.cuda.get_device_name()}")
            logger.info(f"🚀 [GPU] Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    def _get_best_device(self) -> torch.device:
        """Get the best available device"""
        if torch.cuda.is_available():
            return torch.device('cuda')
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return torch.device('mps')
        else:
            return torch.device('cpu')
    
    def optimize_for_gpu(self, model: nn.Module) -> nn.Module:
        """Optimize model for GPU execution"""
        model = model.to(self.device)
        
        if self.cuda_available:
            # Enable mixed precision if supported
            if self.mixed_precision:
                model = model.half()  # Convert to FP16
                logger.info("✅ [GPU] Enabled FP16 mixed precision")
            
            # Optimize CUDA kernels
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            
            # Enable tensor core usage
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True
            
            logger.info("✅ [GPU] GPU optimizations applied")
        
        return model
    
    def create_inference_stream(self) -> Optional[torch.cuda.Stream]:
        """Create CUDA stream for async inference"""
        if self.cuda_available:
            return torch.cuda.Stream()
        return None
    
    def warm_up_gpu(self, model: nn.Module, input_shape: Tuple[int, ...], 
                   num_warmup: int = 10):
        """Warm up GPU for consistent timing"""
        if not self.cuda_available:
            return
        
        dummy_input = torch.randn(input_shape).to(self.device)
        if self.mixed_precision:
            dummy_input = dummy_input.half()
        
        model.eval()
        with torch.no_grad():
            for _ in range(num_warmup):
                _ = model(dummy_input)
        
        torch.cuda.synchronize()
        logger.info(f"🔥 [GPU] Warmed up with {num_warmup} iterations")

class InferenceOptimizer:
    """Optimizes inference pipeline for real-time trading"""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.model_cache = {}
        self.batch_queue = queue.Queue(maxsize=1000)
        self.result_queue = queue.Queue(maxsize=1000)
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Performance monitoring
        self.inference_times = []
        self.throughput_history = []
        
    def optimize_model(self, model: nn.Module, model_name: str,
                      sample_input: torch.Tensor) -> nn.Module:
        """Comprehensive model optimization"""
        logger.info(f"🔧 [OPTIMIZE] Optimizing model: {model_name}")
        
        original_metrics = self._benchmark_model(model, sample_input)
        logger.info(f"📊 [BASELINE] Original inference time: {original_metrics.inference_time_ms:.2f}ms")
        
        optimized_model = model
        
        # Apply optimizations based on config
        if self.config.enable_gpu_acceleration:
            gpu_accelerator = GPUAccelerator()
            optimized_model = gpu_accelerator.optimize_for_gpu(optimized_model)
            gpu_accelerator.warm_up_gpu(optimized_model, sample_input.shape)
        
        if self.config.enable_jit_compilation:
            optimized_model = self._apply_jit_compilation(optimized_model, sample_input)
        
        if self.config.enable_quantization:
            compressor = ModelCompressor()
            optimized_model = compressor.compress_model(optimized_model, 'quantization')
        
        # Benchmark optimized model
        optimized_metrics = self._benchmark_model(optimized_model, sample_input)
        
        # Calculate improvement
        speedup = original_metrics.inference_time_ms / optimized_metrics.inference_time_ms
        memory_reduction = (original_metrics.memory_usage_mb - optimized_metrics.memory_usage_mb) / original_metrics.memory_usage_mb
        
        logger.info(f"✅ [OPTIMIZE] Optimization complete:")
        logger.info(f"   📈 Speedup: {speedup:.2f}x")
        logger.info(f"   🧠 Memory reduction: {memory_reduction:.1%}")
        logger.info(f"   ⚡ New inference time: {optimized_metrics.inference_time_ms:.2f}ms")
        
        # Cache optimized model
        self.model_cache[model_name] = {
            'model': optimized_model,
            'metrics': optimized_metrics,
            'sample_input': sample_input
        }
        
        return optimized_model
    
    def _apply_jit_compilation(self, model: nn.Module, sample_input: torch.Tensor) -> nn.Module:
        """Apply TorchScript JIT compilation"""
        try:
            model.eval()
            with torch.no_grad():
                traced_model = torch.jit.trace(model, sample_input)
                traced_model = torch.jit.optimize_for_inference(traced_model)
            
            logger.info("✅ [JIT] TorchScript compilation successful")
            return traced_model
            
        except Exception as e:
            logger.warning(f"⚠️ [JIT] Compilation failed: {e}")
            return model
    
    def _benchmark_model(self, model: nn.Module, sample_input: torch.Tensor,
                        num_runs: int = 100) -> PerformanceMetrics:
        """Benchmark model performance"""
        model.eval()
        device = next(model.parameters()).device
        sample_input = sample_input.to(device)
        
        # Warm up
        with torch.no_grad():
            for _ in range(10):
                _ = model(sample_input)
        
        if device.type == 'cuda':
            torch.cuda.synchronize()
        
        # Benchmark inference time
        inference_times = []
        
        with torch.no_grad():
            for _ in range(num_runs):
                start_time = time.perf_counter()
                _ = model(sample_input)
                
                if device.type == 'cuda':
                    torch.cuda.synchronize()
                
                end_time = time.perf_counter()
                inference_times.append((end_time - start_time) * 1000)  # Convert to ms
        
        # Calculate metrics
        avg_inference_time = np.mean(inference_times)
        throughput = 1000 / avg_inference_time  # samples per second
        
        # Memory usage
        if device.type == 'cuda':
            memory_usage = torch.cuda.memory_allocated() / (1024 * 1024)  # MB
            gpu_utilization = torch.cuda.utilization() if hasattr(torch.cuda, 'utilization') else 0.0
        else:
            memory_usage = psutil.Process().memory_info().rss / (1024 * 1024)
            gpu_utilization = 0.0
        
        cpu_utilization = psutil.cpu_percent()
        
        # Model size
        model_size = sum(p.numel() * p.element_size() for p in model.parameters()) / (1024 * 1024)
        
        return PerformanceMetrics(
            inference_time_ms=avg_inference_time,
            throughput_samples_per_sec=throughput,
            memory_usage_mb=memory_usage,
            gpu_utilization_percent=gpu_utilization,
            cpu_utilization_percent=cpu_utilization,
            model_size_mb=model_size,
            accuracy_retention=1.0  # Would need validation data to calculate
        )
    
    def create_batch_inference_pipeline(self, model: nn.Module, 
                                      max_batch_size: int = 32) -> callable:
        """Create batched inference pipeline for higher throughput"""
        def batch_inference_worker():
            batch_inputs = []
            batch_ids = []
            
            while True:
                try:
                    # Collect batch
                    while len(batch_inputs) < max_batch_size:
                        try:
                            item_id, input_tensor = self.batch_queue.get(timeout=0.001)
                            batch_inputs.append(input_tensor)
                            batch_ids.append(item_id)
                        except queue.Empty:
                            break
                    
                    if batch_inputs:
                        # Process batch
                        batch_tensor = torch.stack(batch_inputs)
                        
                        with torch.no_grad():
                            batch_outputs = model(batch_tensor)
                        
                        # Return results
                        for i, output in enumerate(batch_outputs):
                            self.result_queue.put((batch_ids[i], output))
                        
                        batch_inputs.clear()
                        batch_ids.clear()
                
                except Exception as e:
                    logger.error(f"Batch inference error: {e}")
        
        # Start worker thread
        worker_thread = threading.Thread(target=batch_inference_worker, daemon=True)
        worker_thread.start()
        
        def submit_inference(input_tensor: torch.Tensor) -> str:
            item_id = f"inference_{time.time()}"
            self.batch_queue.put((item_id, input_tensor))
            return item_id
        
        return submit_inference
    
    def get_inference_result(self, item_id: str, timeout: float = 1.0) -> Optional[torch.Tensor]:
        """Get inference result by ID"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                result_id, output = self.result_queue.get(timeout=0.1)
                if result_id == item_id:
                    return output
                else:
                    # Put back if not our result
                    self.result_queue.put((result_id, output))
            except queue.Empty:
                continue
        
        return None
    
    def monitor_performance(self) -> Dict[str, float]:
        """Monitor real-time performance metrics"""
        if len(self.inference_times) < 10:
            return {}
        
        recent_times = self.inference_times[-100:]  # Last 100 inferences
        
        return {
            'avg_inference_time_ms': np.mean(recent_times),
            'p95_inference_time_ms': np.percentile(recent_times, 95),
            'p99_inference_time_ms': np.percentile(recent_times, 99),
            'throughput_samples_per_sec': len(recent_times) / sum(recent_times) * 1000,
            'queue_size': self.batch_queue.qsize(),
            'pending_results': self.result_queue.qsize()
        }
    
    def save_optimized_model(self, model_name: str, path: str):
        """Save optimized model"""
        if model_name not in self.model_cache:
            raise ValueError(f"Model {model_name} not found in cache")
        
        model_info = self.model_cache[model_name]
        
        torch.save({
            'model': model_info['model'],
            'metrics': model_info['metrics'],
            'optimization_config': self.config
        }, path)
        
        logger.info(f"💾 [SAVE] Optimized model saved to {path}")
    
    def load_optimized_model(self, path: str) -> Tuple[nn.Module, PerformanceMetrics]:
        """Load optimized model"""
        checkpoint = torch.load(path, map_location='cpu')
        
        model = checkpoint['model']
        metrics = checkpoint['metrics']
        
        logger.info(f"📂 [LOAD] Optimized model loaded from {path}")
        return model, metrics
