"""
Live Market Monitoring and Circuit Breaker System
NO MOCK/TEST/SIMULATION - PRODUCTION ONLY
"""

import asyncio
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class MarketCondition:
    """Real market condition data with enhanced sentiment and on-chain metrics"""
    timestamp: datetime
    volatility: float
    price_change: float
    volume_spike: bool
    anomaly_score: float
    risk_level: str
    # Enhanced market factors from live data fetcher
    fear_greed_index: Optional[float] = None
    fear_greed_sentiment: Optional[str] = None
    eth_balance_changes: Optional[Dict[str, float]] = None
    glassnode_metrics: Optional[Dict[str, float]] = None
    market_sentiment_score: Optional[float] = None

class CircuitBreaker:
    """
    Production Circuit Breaker for Live Trading
    Halts trading during dangerous market conditions
    """
    
    def __init__(self, volatility_threshold: float = 0.15, price_drop_limit: float = 0.05):
        self.volatility_threshold = volatility_threshold
        self.price_drop_limit = price_drop_limit
        self.is_halted = False
        self.halt_reason = None
        self.halt_timestamp = None
        
    async def should_halt_trading(self, market_data: Dict) -> bool:
        """
        Determine if trading should be halted based on real market conditions
        
        Args:
            market_data: Real market data from exchanges
            
        Returns:
            bool: True if trading should be halted
        """
        try:
            # Analyze real market conditions with enhanced data
            conditions = await self._analyze_market_conditions(market_data)

            # Integrate enhanced market factors from live data fetcher
            enhanced_conditions = await self._integrate_enhanced_market_data(conditions, market_data)
            
            # Check for halt conditions
            halt_triggers = [
                self._check_volatility_spike(conditions),
                self._check_price_crash(conditions),
                self._check_volume_anomaly(conditions),
                self._check_market_anomalies(conditions)
            ]
            
            should_halt = any(halt_triggers)
            
            if should_halt and not self.is_halted:
                self.is_halted = True
                self.halt_timestamp = datetime.now()
                self.halt_reason = self._determine_halt_reason(halt_triggers)
                logger.warning(f"[CIRCUIT BREAKER] Trading halted: {self.halt_reason}")
                
            elif not should_halt and self.is_halted:
                # Check if enough time has passed to resume
                if self._can_resume_trading():
                    self.is_halted = False
                    self.halt_reason = None
                    self.halt_timestamp = None
                    logger.info("[CIRCUIT BREAKER] Trading resumed - conditions normalized")
            
            return self.is_halted
            
        except Exception as e:
            logger.error(f"Circuit breaker analysis failed: {e}")
            # Default to halt on analysis failure for safety
            return True
    
    async def _analyze_market_conditions(self, market_data: Dict) -> MarketCondition:
        """Analyze real market conditions"""
        try:
            # Check if we're using fallback data (when exchange APIs are unavailable)
            is_fallback_data = False
            try:
                market_data_str = str(market_data)
                is_fallback_data = ('fallback_data' in market_data_str or
                                   'synthetic' in market_data_str)

                # Safely check keys for fallback indicators
                if not is_fallback_data and hasattr(market_data, 'keys'):
                    for k in market_data.keys():
                        try:
                            key_str = str(k)
                            if 'fallback_data' in key_str or 'synthetic' in key_str:
                                is_fallback_data = True
                                break
                        except (TypeError, AttributeError):
                            # Skip keys that can't be converted to string (like datetime objects)
                            continue
            except Exception:
                is_fallback_data = False

            # Extract price data from market data
            prices = []
            volumes = []

            # Handle structured market data format
            if 'price_data' in market_data:
                for exchange, symbols in market_data['price_data'].items():
                    for symbol, data in symbols.items():
                        if isinstance(data, dict):
                            if 'price' in data:
                                prices.append(float(data['price']))
                            if 'volume' in data:
                                volumes.append(float(data['volume']))

            # Fallback to old format
            for key, data in market_data.items():
                try:
                    key_str = str(key)
                    if 'ticker' in key_str and isinstance(data, dict):
                        if 'price' in data:
                            prices.append(float(data['price']))
                        elif 'last' in data:
                            prices.append(float(data['last']))

                    if isinstance(data, dict) and 'volume' in data:
                        volumes.append(float(data['volume']))
                except (TypeError, AttributeError, ValueError):
                    # Skip problematic keys/data
                    continue
            
            if not prices:
                # CRITICAL: NO SYNTHETIC DATA - FAIL GRACEFULLY WITH REAL DATA REQUIREMENT
                logger.error("[CRITICAL] No real price data available for market condition analysis")
                logger.error("[CRITICAL] Cannot generate synthetic market conditions - system requires real data")
                return MarketCondition(
                    timestamp=datetime.now(),
                    volatility=0.0,  # No data available
                    price_change=0.0,  # No data available
                    volume_spike=False,
                    anomaly_score=0.0,  # No data to analyze
                    risk_level="UNKNOWN"  # Cannot determine without real data
                )
            
            # Calculate volatility
            if len(prices) > 1:
                price_changes = np.diff(prices) / prices[:-1]
                volatility = np.std(price_changes)
                price_change = price_changes[-1] if len(price_changes) > 0 else 0
            else:
                volatility = 0
                price_change = 0
            
            # Check volume spike - more lenient threshold
            volume_spike = len(volumes) > 0 and max(volumes) > np.mean(volumes) * 5 if len(volumes) > 1 else False

            # Calculate anomaly score using REAL data only - no fallback adjustments
            anomaly_multiplier = 0.3  # Standard multiplier for real data only
            anomaly_score = min((volatility * 5 + abs(price_change) * 2) * anomaly_multiplier, 1.0)

            # Determine risk level using standard thresholds - no fallback adjustments
            volatility_thresh = self.volatility_threshold
            price_thresh = self.price_drop_limit

            if volatility > volatility_thresh or abs(price_change) > price_thresh:
                risk_level = "HIGH"
            elif volatility > volatility_thresh * 0.7:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"
            
            return MarketCondition(
                timestamp=datetime.now(),
                volatility=volatility,
                price_change=price_change,
                volume_spike=volume_spike,
                anomaly_score=anomaly_score,
                risk_level=risk_level
            )
            
        except Exception as e:
            logger.error(f"Market condition analysis failed: {e}")
            # CRITICAL: NO FALLBACK TO DEFAULT VALUES - SYSTEM MUST USE REAL DATA
            logger.error("[CRITICAL] Market condition analysis requires real data - no fallback allowed")
            raise RuntimeError(f"Market condition analysis failed - no fallback available: {e}")
    
    def _check_volatility_spike(self, conditions: MarketCondition) -> bool:
        """Check for dangerous volatility spikes"""
        return conditions.volatility > self.volatility_threshold
    
    def _check_price_crash(self, conditions: MarketCondition) -> bool:
        """Check for rapid price crashes"""
        return conditions.price_change < -self.price_drop_limit
    
    def _check_volume_anomaly(self, conditions: MarketCondition) -> bool:
        """Check for abnormal volume spikes - more lenient"""
        # Only halt on extreme volume spikes with very high risk
        return conditions.volume_spike and conditions.risk_level == "HIGH" and conditions.anomaly_score > 0.9
    
    def _check_market_anomalies(self, conditions: MarketCondition) -> bool:
        """Check for general market anomalies - more lenient for normal trading"""
        return conditions.anomaly_score > 0.95  # Much higher threshold
    
    def _determine_halt_reason(self, halt_triggers: List[bool]) -> str:
        """Determine the reason for trading halt"""
        reasons = []
        
        if halt_triggers[0]:
            reasons.append("Excessive volatility")
        if halt_triggers[1]:
            reasons.append("Rapid price decline")
        if halt_triggers[2]:
            reasons.append("Volume anomaly")
        if halt_triggers[3]:
            reasons.append("Market anomaly detected")
        
        return "; ".join(reasons) if reasons else "Unknown risk condition"
    
    def _can_resume_trading(self) -> bool:
        """Check if enough time has passed to resume trading"""
        if not self.halt_timestamp:
            return True
        
        # Require at least 5 minutes halt before resuming
        time_since_halt = datetime.now() - self.halt_timestamp
        return time_since_halt > timedelta(minutes=5)

class MarketAnomalyDetector:
    """
    Production Market Anomaly Detection System
    Detects unusual market patterns that could indicate risks
    """
    
    def __init__(self):
        self.historical_data = []
        self.anomaly_threshold = 0.8
        
    async def detect_anomalies(self, market_data: Dict) -> List[Dict]:
        """
        Detect market anomalies in real-time data
        
        Args:
            market_data: Real market data from exchanges
            
        Returns:
            List of detected anomalies
        """
        anomalies = []
        
        try:
            # Store historical data
            self.historical_data.append({
                'timestamp': datetime.now(),
                'data': market_data
            })
            
            # Keep only recent data (last 100 points)
            if len(self.historical_data) > 100:
                self.historical_data = self.historical_data[-100:]
            
            # Need at least 10 data points for anomaly detection
            if len(self.historical_data) < 10:
                return anomalies
            
            # Detect price anomalies
            price_anomalies = await self._detect_price_anomalies()
            anomalies.extend(price_anomalies)
            
            # Detect volume anomalies  
            volume_anomalies = await self._detect_volume_anomalies()
            anomalies.extend(volume_anomalies)
            
            # Detect spread anomalies
            spread_anomalies = await self._detect_spread_anomalies()
            anomalies.extend(spread_anomalies)
            
            if anomalies:
                logger.warning(f"[ANOMALY DETECTION] Found {len(anomalies)} market anomalies")
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Anomaly detection failed: {e}")
            return []  # Return empty list on error
    
    async def _detect_price_anomalies(self) -> List[Dict]:
        """Detect unusual price movements"""
        anomalies = []
        
        try:
            # Extract price series
            prices = []
            for entry in self.historical_data[-20:]:  # Last 20 data points
                for key, data in entry['data'].items():
                    if 'ticker' in key and isinstance(data, dict):
                        if 'price' in data:
                            prices.append(float(data['price']))
                        elif 'last' in data:
                            prices.append(float(data['last']))
                        break  # Use first price found
            
            if len(prices) < 10:
                return anomalies
            
            # Calculate z-scores for recent prices
            recent_prices = np.array(prices[-10:])
            mean_price = np.mean(prices[:-10]) if len(prices) > 10 else np.mean(prices)
            std_price = np.std(prices[:-10]) if len(prices) > 10 else np.std(prices)
            
            if std_price > 0:
                z_scores = np.abs((recent_prices - mean_price) / std_price)
                
                # Flag prices with z-score > 3 (very unusual)
                for i, z_score in enumerate(z_scores):
                    if z_score > 3:
                        anomalies.append({
                            'type': 'price_anomaly',
                            'severity': 'high' if z_score > 4 else 'medium',
                            'z_score': float(z_score),
                            'price': float(recent_prices[i]),
                            'timestamp': datetime.now()
                        })
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Price anomaly detection failed: {e}")
            return []
    
    async def _detect_volume_anomalies(self) -> List[Dict]:
        """Detect unusual volume patterns"""
        anomalies = []
        
        try:
            # Extract volume series
            volumes = []
            for entry in self.historical_data[-20:]:
                for key, data in entry['data'].items():
                    if isinstance(data, dict) and 'volume' in data:
                        volumes.append(float(data['volume']))
                        break
            
            if len(volumes) < 10:
                return anomalies
            
            # Calculate volume anomalies
            recent_volumes = np.array(volumes[-5:])
            historical_mean = np.mean(volumes[:-5]) if len(volumes) > 5 else np.mean(volumes)
            
            # Flag volumes that are > 5x historical average
            for i, volume in enumerate(recent_volumes):
                if volume > historical_mean * 5:
                    anomalies.append({
                        'type': 'volume_anomaly',
                        'severity': 'high',
                        'volume': float(volume),
                        'multiplier': float(volume / historical_mean),
                        'timestamp': datetime.now()
                    })
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Volume anomaly detection failed: {e}")
            return []
    
    async def _detect_spread_anomalies(self) -> List[Dict]:
        """Detect unusual bid-ask spread patterns"""
        anomalies = []
        
        try:
            # Extract spread data from orderbooks
            spreads = []
            for entry in self.historical_data[-10:]:
                for key, data in entry['data'].items():
                    if 'orderbook' in key and isinstance(data, dict):
                        if 'bids' in data and 'asks' in data and data['bids'] and data['asks']:
                            bid = float(data['bids'][0][0])
                            ask = float(data['asks'][0][0])
                            spread = (ask - bid) / bid  # Relative spread
                            spreads.append(spread)
                        break
            
            if len(spreads) < 5:
                return anomalies
            
            # Flag unusually wide spreads
            mean_spread = np.mean(spreads[:-2]) if len(spreads) > 2 else np.mean(spreads)
            recent_spreads = spreads[-2:]
            
            for spread in recent_spreads:
                if spread > mean_spread * 3:  # 3x normal spread
                    anomalies.append({
                        'type': 'spread_anomaly',
                        'severity': 'medium',
                        'spread': float(spread),
                        'normal_spread': float(mean_spread),
                        'timestamp': datetime.now()
                    })
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Spread anomaly detection failed: {e}")
            return []

    async def _integrate_enhanced_market_data(self, conditions: MarketCondition, market_data: Dict) -> MarketCondition:
        """
        Integrate enhanced market data from live data fetcher
        Including Fear & Greed Index, Etherscan data, and Glassnode metrics
        """
        try:
            # Extract enhanced data from live data fetcher
            live_aggregated = market_data.get('live_aggregated', {})

            # Fear & Greed Index integration
            fear_greed_index = live_aggregated.get('fear_greed_index')
            fear_greed_classification = live_aggregated.get('fear_greed_classification')

            if fear_greed_index is not None:
                conditions.fear_greed_index = float(fear_greed_index)

                # Convert numeric classification to sentiment
                if fear_greed_index <= 25:
                    conditions.fear_greed_sentiment = "extreme_fear"
                elif fear_greed_index <= 45:
                    conditions.fear_greed_sentiment = "fear"
                elif fear_greed_index <= 55:
                    conditions.fear_greed_sentiment = "neutral"
                elif fear_greed_index <= 75:
                    conditions.fear_greed_sentiment = "greed"
                else:
                    conditions.fear_greed_sentiment = "extreme_greed"

                logger.info(f"📊 [MARKET-MONITOR] Fear & Greed Index: {fear_greed_index} ({conditions.fear_greed_sentiment})")

            # Etherscan balance tracking
            eth_balances = {}
            for key, value in live_aggregated.items():
                if 'etherscan' in key and 'balance_eth' in key:
                    address_id = key.split('_')[1]  # Extract address identifier
                    eth_balances[address_id] = float(value)

            if eth_balances:
                conditions.eth_balance_changes = eth_balances
                logger.info(f"📊 [MARKET-MONITOR] Tracking {len(eth_balances)} Ethereum addresses")

            # Glassnode on-chain metrics
            glassnode_data = {}
            for key, value in live_aggregated.items():
                if 'glassnode' in key:
                    metric_name = key.replace('glassnode_', '')
                    glassnode_data[metric_name] = float(value)

            if glassnode_data:
                conditions.glassnode_metrics = glassnode_data
                logger.info(f"📊 [MARKET-MONITOR] Glassnode metrics: {list(glassnode_data.keys())}")

            # Calculate composite market sentiment score
            sentiment_score = self._calculate_market_sentiment_score(conditions)
            conditions.market_sentiment_score = sentiment_score

            # Adjust risk level based on enhanced factors
            enhanced_risk = self._calculate_enhanced_risk_level(conditions)
            if enhanced_risk != conditions.risk_level:
                logger.warning(f"📊 [MARKET-MONITOR] Risk level adjusted: {conditions.risk_level} -> {enhanced_risk}")
                conditions.risk_level = enhanced_risk

            return conditions

        except Exception as e:
            logger.error(f"❌ [MARKET-MONITOR] Enhanced data integration failed: {e}")
            return conditions

    def _calculate_market_sentiment_score(self, conditions: MarketCondition) -> float:
        """Calculate composite market sentiment score from multiple factors"""
        try:
            sentiment_factors = []

            # Fear & Greed Index (0-100, normalize to -1 to 1)
            if conditions.fear_greed_index is not None:
                fg_normalized = (conditions.fear_greed_index - 50) / 50  # -1 to 1
                sentiment_factors.append(fg_normalized * 0.4)  # 40% weight

            # Price momentum (from existing volatility/price_change)
            if conditions.price_change is not None:
                price_momentum = np.tanh(conditions.price_change * 10)  # Normalize to -1 to 1
                sentiment_factors.append(price_momentum * 0.3)  # 30% weight

            # Volatility factor (high volatility = negative sentiment)
            if conditions.volatility is not None:
                vol_factor = -np.tanh(conditions.volatility * 5)  # High vol = negative
                sentiment_factors.append(vol_factor * 0.2)  # 20% weight

            # Glassnode metrics (if available)
            if conditions.glassnode_metrics:
                # Example: MVRV ratio, NVT ratio, etc.
                glassnode_sentiment = 0
                metric_count = 0

                for metric, value in conditions.glassnode_metrics.items():
                    if 'mvrv' in metric.lower():
                        # MVRV > 3.7 = overvalued, < 1 = undervalued
                        mvrv_sentiment = np.tanh((value - 2.35) / 1.35)  # Normalize around 2.35
                        glassnode_sentiment += mvrv_sentiment
                        metric_count += 1
                    elif 'nvt' in metric.lower():
                        # NVT > 95 = overvalued, < 45 = undervalued
                        nvt_sentiment = -np.tanh((value - 70) / 25)  # Normalize around 70
                        glassnode_sentiment += nvt_sentiment
                        metric_count += 1

                if metric_count > 0:
                    sentiment_factors.append((glassnode_sentiment / metric_count) * 0.1)  # 10% weight

            # Calculate weighted average
            if sentiment_factors:
                composite_score = sum(sentiment_factors)
                return max(-1.0, min(1.0, composite_score))  # Clamp to [-1, 1]

            return 0.0  # Neutral if no factors available

        except Exception as e:
            logger.error(f"❌ [MARKET-MONITOR] Sentiment score calculation failed: {e}")
            return 0.0

    def _calculate_enhanced_risk_level(self, conditions: MarketCondition) -> str:
        """Calculate enhanced risk level using all available factors"""
        try:
            risk_score = 0.0

            # Base risk from existing factors
            if conditions.volatility and conditions.volatility > self.volatility_threshold:
                risk_score += 0.3

            if conditions.price_change and abs(conditions.price_change) > self.price_drop_limit:
                risk_score += 0.3

            if conditions.anomaly_score and conditions.anomaly_score > 0.7:
                risk_score += 0.2

            # Enhanced risk factors
            if conditions.fear_greed_index is not None:
                # Extreme fear or extreme greed both increase risk
                if conditions.fear_greed_index <= 20 or conditions.fear_greed_index >= 80:
                    risk_score += 0.2
                elif conditions.fear_greed_index <= 30 or conditions.fear_greed_index >= 70:
                    risk_score += 0.1

            # Market sentiment risk
            if conditions.market_sentiment_score is not None:
                # Extreme sentiment (positive or negative) increases risk
                sentiment_risk = abs(conditions.market_sentiment_score)
                if sentiment_risk > 0.8:
                    risk_score += 0.2
                elif sentiment_risk > 0.6:
                    risk_score += 0.1

            # Determine risk level
            if risk_score >= 0.7:
                return "critical"
            elif risk_score >= 0.5:
                return "high"
            elif risk_score >= 0.3:
                return "medium"
            else:
                return "low"

        except Exception as e:
            logger.error(f"❌ [MARKET-MONITOR] Enhanced risk calculation failed: {e}")
            return conditions.risk_level  # Return original risk level

# Export real components only
__all__ = ['CircuitBreaker', 'MarketAnomalyDetector']
