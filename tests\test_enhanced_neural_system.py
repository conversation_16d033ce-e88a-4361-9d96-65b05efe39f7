#!/usr/bin/env python3
"""
Comprehensive Neural System Testing
Tests all enhanced neural components with new feature inputs and validates performance
"""

import sys
import os
import asyncio
import logging
import traceback
from datetime import datetime, timezone
import json

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedNeuralSystemTester:
    """Comprehensive tester for enhanced neural trading system"""
    
    def __init__(self):
        self.test_results = {}
        self.components = {}
        self.test_market_data = self._generate_test_market_data()
        self.test_sentiment_data = self._generate_test_sentiment_data()
        
    def _generate_test_market_data(self):
        """Generate comprehensive test market data"""
        return {
            'live_aggregated': {
                'fear_greed_index': 65.0,
                'ethereum_data': {
                    'gas_price': 25.0,
                    'network_utilization': 0.7,
                    'active_addresses': 750000,
                    'transaction_count': 1500000,
                    'whale_activity': 0.3,
                    'defi_tvl_change': 0.05,
                    'exchange_flows': -0.2
                },
                'bitcoin_data': {
                    'hash_rate': 350e18,
                    'difficulty_adjustment': 0.02,
                    'mempool_size': 75,
                    'active_addresses': 900000,
                    'hodl_waves': 0.65,
                    'exchange_flows': -0.1
                },
                'defi_data': {
                    'total_value_locked': 75e9,
                    'yield_farming_apy': 0.08,
                    'liquidation_risk': 0.15,
                    'governance_activity': 0.4,
                    'new_protocol_launches': 2,
                    'protocol_security_incidents': 0
                },
                'institutional_data': {
                    'etf_flows': 500e6,
                    'corporate_treasury': 0.1,
                    'custody_growth': 0.15,
                    'derivatives_oi': 0.2,
                    'regulatory_clarity': 0.6,
                    'institutional_sentiment': 0.3
                }
            },
            'web_crawler_insights': {
                'overall_sentiment': 0.4,
                'confidence': 0.8,
                'high_impact_count': 3,
                'sentiment_sources': ['twitter', 'reddit', 'news']
            },
            'price_data': {
                'BTC-USD': {
                    'price': 45000.0,
                    'price_change_24h': 2.5,
                    'volume': 25000000000,
                    'volume_change_24h': 15.0,
                    'volatility': 0.08
                },
                'ETH-USD': {
                    'price': 2800.0,
                    'price_change_24h': 3.2,
                    'volume': 12000000000,
                    'volume_change_24h': 20.0,
                    'volatility': 0.12
                },
                'SOL-USD': {
                    'price': 95.0,
                    'price_change_24h': 5.1,
                    'volume': 2000000000,
                    'volume_change_24h': 25.0,
                    'volatility': 0.15
                }
            },
            'technical_indicators': {
                'rsi': 58.0,
                'macd': 0.02,
                'macd_signal': 0.015,
                'bollinger_position': 0.6
            }
        }
    
    def _generate_test_sentiment_data(self):
        """Generate test sentiment analysis data"""
        return {
            'aggregated_sentiment': 0.35,
            'confidence': 0.75,
            'signal_strength': 0.6,
            'sources': ['fear_greed', 'web_crawler', 'technical']
        }
    
    async def run_comprehensive_tests(self):
        """Run all comprehensive neural system tests"""
        logger.info("🧪 Starting Comprehensive Neural System Testing...")
        
        try:
            # Test 1: Component Initialization
            await self._test_component_initialization()
            
            # Test 2: Feature Engineering
            await self._test_feature_engineering()
            
            # Test 3: Sentiment Scoring
            await self._test_composite_sentiment_scoring()
            
            # Test 4: Market Regime Detection
            await self._test_market_regime_detection()
            
            # Test 5: Enhanced Neural Predictions
            await self._test_enhanced_neural_predictions()
            
            # Test 6: Reinforcement Learning
            await self._test_reinforcement_learning()
            
            # Test 7: Graph Neural Network
            await self._test_graph_neural_network()
            
            # Test 8: Integration Testing
            await self._test_system_integration()
            
            # Test 9: Performance Validation
            await self._test_performance_metrics()
            
            # Generate comprehensive report
            self._generate_test_report()
            
            return self.test_results
            
        except Exception as e:
            logger.error(f"❌ Comprehensive testing failed: {e}")
            logger.error(traceback.format_exc())
            return {'error': str(e), 'traceback': traceback.format_exc()}
    
    async def _test_component_initialization(self):
        """Test initialization of all enhanced neural components"""
        logger.info("🔧 Testing Component Initialization...")
        
        test_name = "component_initialization"
        self.test_results[test_name] = {'status': 'running', 'details': {}}
        
        try:
            # Test Enhanced Profit Predictor
            try:
                from src.neural.enhanced_profit_predictor import EnhancedProfitPredictor
                profit_predictor = EnhancedProfitPredictor({
                    'lookback_window': 100,
                    'prediction_horizons': ['1h', '4h', '1d'],
                    'confidence_threshold': 0.6,
                    'feature_engineering': True,
                    'sentiment_integration': True,
                    'fear_greed_weight': 0.35
                })
                self.components['profit_predictor'] = profit_predictor
                self.test_results[test_name]['details']['profit_predictor'] = 'success'
                logger.info("✅ Enhanced Profit Predictor initialized")
            except Exception as e:
                self.test_results[test_name]['details']['profit_predictor'] = f'failed: {e}'
                logger.error(f"❌ Enhanced Profit Predictor failed: {e}")
            
            # Test Feature Engineer
            try:
                from src.neural.feature_engineering import FeatureEngineer
                feature_engineer = FeatureEngineer(history_length=168)
                self.components['feature_engineer'] = feature_engineer
                self.test_results[test_name]['details']['feature_engineer'] = 'success'
                logger.info("✅ Feature Engineer initialized")
            except Exception as e:
                self.test_results[test_name]['details']['feature_engineer'] = f'failed: {e}'
                logger.error(f"❌ Feature Engineer failed: {e}")
            
            # Test Composite Sentiment Scorer
            try:
                from src.neural.composite_sentiment_scorer import CompositeSentimentScorer
                sentiment_scorer = CompositeSentimentScorer()
                self.components['sentiment_scorer'] = sentiment_scorer
                self.test_results[test_name]['details']['sentiment_scorer'] = 'success'
                logger.info("✅ Composite Sentiment Scorer initialized")
            except Exception as e:
                self.test_results[test_name]['details']['sentiment_scorer'] = f'failed: {e}'
                logger.error(f"❌ Composite Sentiment Scorer failed: {e}")
            
            # Test Market Regime Detector
            try:
                from src.neural.market_regime_detector import MarketRegimeDetector
                regime_detector = MarketRegimeDetector()
                self.components['regime_detector'] = regime_detector
                self.test_results[test_name]['details']['regime_detector'] = 'success'
                logger.info("✅ Market Regime Detector initialized")
            except Exception as e:
                self.test_results[test_name]['details']['regime_detector'] = f'failed: {e}'
                logger.error(f"❌ Market Regime Detector failed: {e}")
            
            # Test Enhanced LSTM
            try:
                from src.neural.lstm_processor import LSTMProcessor
                lstm_processor = LSTMProcessor(input_size=30, hidden_size=128, num_layers=3)
                self.components['lstm_processor'] = lstm_processor
                self.test_results[test_name]['details']['lstm_processor'] = 'success'
                logger.info("✅ Enhanced LSTM Processor initialized")
            except Exception as e:
                self.test_results[test_name]['details']['lstm_processor'] = f'failed: {e}'
                logger.error(f"❌ Enhanced LSTM Processor failed: {e}")
            
            # Test Reinforcement Learning Agent
            try:
                from src.neural.reinforcement_learning import ReinforcementLearningAgent
                rl_agent = ReinforcementLearningAgent(
                    state_size=30, action_size=5, learning_rate=0.001
                )
                self.components['rl_agent'] = rl_agent
                self.test_results[test_name]['details']['rl_agent'] = 'success'
                logger.info("✅ Reinforcement Learning Agent initialized")
            except Exception as e:
                self.test_results[test_name]['details']['rl_agent'] = f'failed: {e}'
                logger.error(f"❌ Reinforcement Learning Agent failed: {e}")
            
            # Calculate success rate
            successful_components = sum(1 for result in self.test_results[test_name]['details'].values() 
                                      if result == 'success')
            total_components = len(self.test_results[test_name]['details'])
            success_rate = successful_components / total_components if total_components > 0 else 0
            
            self.test_results[test_name]['status'] = 'completed'
            self.test_results[test_name]['success_rate'] = success_rate
            self.test_results[test_name]['successful_components'] = successful_components
            self.test_results[test_name]['total_components'] = total_components
            
            logger.info(f"🔧 Component Initialization: {successful_components}/{total_components} "
                       f"components successful ({success_rate:.1%})")
            
        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Component initialization test failed: {e}")
    
    async def _test_feature_engineering(self):
        """Test feature engineering with new inputs"""
        logger.info("🔍 Testing Feature Engineering...")
        
        test_name = "feature_engineering"
        self.test_results[test_name] = {'status': 'running', 'details': {}}
        
        try:
            if 'feature_engineer' not in self.components:
                self.test_results[test_name]['status'] = 'skipped'
                self.test_results[test_name]['reason'] = 'Feature engineer not initialized'
                return
            
            feature_engineer = self.components['feature_engineer']
            
            # Test Fear & Greed feature extraction
            try:
                fg_features = await feature_engineer.extract_fear_greed_features(self.test_market_data)
                self.test_results[test_name]['details']['fear_greed_features'] = {
                    'status': 'success',
                    'feature_count': len(fg_features),
                    'sample_features': list(fg_features.keys())[:5]
                }
                logger.info(f"✅ Fear & Greed features: {len(fg_features)} extracted")
            except Exception as e:
                self.test_results[test_name]['details']['fear_greed_features'] = {
                    'status': 'failed',
                    'error': str(e)
                }
                logger.error(f"❌ Fear & Greed feature extraction failed: {e}")
            
            # Test Etherscan feature extraction
            try:
                etherscan_features = await feature_engineer.extract_etherscan_features(self.test_market_data)
                self.test_results[test_name]['details']['etherscan_features'] = {
                    'status': 'success',
                    'feature_count': len(etherscan_features),
                    'sample_features': list(etherscan_features.keys())[:5]
                }
                logger.info(f"✅ Etherscan features: {len(etherscan_features)} extracted")
            except Exception as e:
                self.test_results[test_name]['details']['etherscan_features'] = {
                    'status': 'failed',
                    'error': str(e)
                }
                logger.error(f"❌ Etherscan feature extraction failed: {e}")
            
            # Test Glassnode feature extraction
            try:
                glassnode_features = await feature_engineer.extract_glassnode_features(self.test_market_data)
                self.test_results[test_name]['details']['glassnode_features'] = {
                    'status': 'success',
                    'feature_count': len(glassnode_features),
                    'sample_features': list(glassnode_features.keys())[:5]
                }
                logger.info(f"✅ Glassnode features: {len(glassnode_features)} extracted")
            except Exception as e:
                self.test_results[test_name]['details']['glassnode_features'] = {
                    'status': 'failed',
                    'error': str(e)
                }
                logger.error(f"❌ Glassnode feature extraction failed: {e}")
            
            # Calculate success rate
            successful_extractions = sum(1 for result in self.test_results[test_name]['details'].values() 
                                       if result.get('status') == 'success')
            total_extractions = len(self.test_results[test_name]['details'])
            success_rate = successful_extractions / total_extractions if total_extractions > 0 else 0
            
            self.test_results[test_name]['status'] = 'completed'
            self.test_results[test_name]['success_rate'] = success_rate
            
            logger.info(f"🔍 Feature Engineering: {successful_extractions}/{total_extractions} "
                       f"extractions successful ({success_rate:.1%})")
            
        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Feature engineering test failed: {e}")
    
    async def _test_composite_sentiment_scoring(self):
        """Test composite sentiment scoring system"""
        logger.info("💭 Testing Composite Sentiment Scoring...")
        
        test_name = "sentiment_scoring"
        self.test_results[test_name] = {'status': 'running', 'details': {}}
        
        try:
            if 'sentiment_scorer' not in self.components:
                self.test_results[test_name]['status'] = 'skipped'
                self.test_results[test_name]['reason'] = 'Sentiment scorer not initialized'
                return
            
            sentiment_scorer = self.components['sentiment_scorer']
            
            # Test composite sentiment calculation
            try:
                sentiment_result = await sentiment_scorer.calculate_composite_sentiment(
                    self.test_market_data, self.test_sentiment_data
                )
                
                self.test_results[test_name]['details']['composite_sentiment'] = {
                    'status': 'success',
                    'composite_score': sentiment_result.composite_score,
                    'confidence': sentiment_result.confidence,
                    'component_count': len(sentiment_result.components),
                    'signal_count': len(sentiment_result.signals)
                }
                logger.info(f"✅ Composite sentiment: {sentiment_result.composite_score:.3f} "
                           f"(confidence: {sentiment_result.confidence:.3f})")
            except Exception as e:
                self.test_results[test_name]['details']['composite_sentiment'] = {
                    'status': 'failed',
                    'error': str(e)
                }
                logger.error(f"❌ Composite sentiment calculation failed: {e}")
            
            # Test sentiment summary
            try:
                sentiment_summary = sentiment_scorer.get_sentiment_summary()
                self.test_results[test_name]['details']['sentiment_summary'] = {
                    'status': 'success',
                    'summary_keys': list(sentiment_summary.keys())
                }
                logger.info("✅ Sentiment summary generated")
            except Exception as e:
                self.test_results[test_name]['details']['sentiment_summary'] = {
                    'status': 'failed',
                    'error': str(e)
                }
                logger.error(f"❌ Sentiment summary failed: {e}")
            
            self.test_results[test_name]['status'] = 'completed'
            
        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Sentiment scoring test failed: {e}")

    async def _test_market_regime_detection(self):
        """Test market regime detection system"""
        logger.info("📊 Testing Market Regime Detection...")

        test_name = "regime_detection"
        self.test_results[test_name] = {'status': 'running', 'details': {}}

        try:
            if 'regime_detector' not in self.components:
                self.test_results[test_name]['status'] = 'skipped'
                self.test_results[test_name]['reason'] = 'Regime detector not initialized'
                return

            regime_detector = self.components['regime_detector']

            # Test regime detection
            try:
                regime_result = await regime_detector.detect_market_regime(
                    self.test_market_data, self.test_sentiment_data, 0.35
                )

                self.test_results[test_name]['details']['regime_detection'] = {
                    'status': 'success',
                    'primary_regime': regime_result.primary_regime.value,
                    'confidence': regime_result.regime_confidence,
                    'probability_count': len(regime_result.regime_probabilities),
                    'factor_count': len(regime_result.regime_factors),
                    'trading_implications': list(regime_result.trading_implications.keys())
                }
                logger.info(f"✅ Market regime: {regime_result.primary_regime.value} "
                           f"(confidence: {regime_result.regime_confidence:.3f})")
            except Exception as e:
                self.test_results[test_name]['details']['regime_detection'] = {
                    'status': 'failed',
                    'error': str(e)
                }
                logger.error(f"❌ Market regime detection failed: {e}")

            self.test_results[test_name]['status'] = 'completed'

        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Market regime detection test failed: {e}")

    async def _test_enhanced_neural_predictions(self):
        """Test enhanced neural prediction models"""
        logger.info("🧠 Testing Enhanced Neural Predictions...")

        test_name = "neural_predictions"
        self.test_results[test_name] = {'status': 'running', 'details': {}}

        try:
            # Test Enhanced Profit Predictor
            if 'profit_predictor' in self.components:
                try:
                    profit_predictor = self.components['profit_predictor']
                    prediction = await profit_predictor.predict_profit(
                        self.test_market_data, 'BTC-USD', self.test_sentiment_data
                    )

                    self.test_results[test_name]['details']['profit_prediction'] = {
                        'status': 'success',
                        'profit_score': prediction.profit_score,
                        'confidence': prediction.confidence,
                        'risk_score': prediction.risk_score
                    }
                    logger.info(f"✅ Profit prediction: {prediction.profit_score:.4f} "
                               f"(confidence: {prediction.confidence:.3f})")
                except Exception as e:
                    self.test_results[test_name]['details']['profit_prediction'] = {
                        'status': 'failed',
                        'error': str(e)
                    }
                    logger.error(f"❌ Profit prediction failed: {e}")

            # Test LSTM Processor
            if 'lstm_processor' in self.components:
                try:
                    lstm_processor = self.components['lstm_processor']
                    # Create test feature vector
                    test_features = {f'feature_{i}': 0.5 for i in range(30)}

                    prediction = lstm_processor.predict(test_features)

                    self.test_results[test_name]['details']['lstm_prediction'] = {
                        'status': 'success',
                        'prediction': prediction.prediction,
                        'confidence': prediction.confidence,
                        'features_used': prediction.features_used
                    }
                    logger.info(f"✅ LSTM prediction: {prediction.prediction:.4f} "
                               f"(confidence: {prediction.confidence:.3f})")
                except Exception as e:
                    self.test_results[test_name]['details']['lstm_prediction'] = {
                        'status': 'failed',
                        'error': str(e)
                    }
                    logger.error(f"❌ LSTM prediction failed: {e}")

            self.test_results[test_name]['status'] = 'completed'

        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Neural predictions test failed: {e}")

    async def _test_reinforcement_learning(self):
        """Test reinforcement learning agent"""
        logger.info("🎯 Testing Reinforcement Learning...")

        test_name = "reinforcement_learning"
        self.test_results[test_name] = {'status': 'running', 'details': {}}

        try:
            if 'rl_agent' not in self.components:
                self.test_results[test_name]['status'] = 'skipped'
                self.test_results[test_name]['reason'] = 'RL agent not initialized'
                return

            rl_agent = self.components['rl_agent']

            # Test action prediction
            try:
                test_state = [0.5] * 30  # 30-dimensional state vector
                action_probs = rl_agent.predict(test_state)
                best_action = max(range(len(action_probs)), key=lambda i: action_probs[i])

                self.test_results[test_name]['details']['action_prediction'] = {
                    'status': 'success',
                    'best_action': best_action,
                    'confidence': action_probs[best_action],
                    'action_space_size': len(action_probs)
                }
                logger.info(f"✅ RL action: {best_action} (confidence: {action_probs[best_action]:.3f})")
            except Exception as e:
                self.test_results[test_name]['details']['action_prediction'] = {
                    'status': 'failed',
                    'error': str(e)
                }
                logger.error(f"❌ RL action prediction failed: {e}")

            # Test enhanced reward calculation
            try:
                trade_result = {
                    'profit_loss': 0.02,
                    'fees': 0.001,
                    'trade_size': 1000.0,
                    'account_balance': 10000.0
                }

                enhanced_reward = rl_agent.calculate_enhanced_reward(
                    trade_result, self.test_market_data, self.test_sentiment_data
                )

                self.test_results[test_name]['details']['enhanced_reward'] = {
                    'status': 'success',
                    'reward_value': enhanced_reward
                }
                logger.info(f"✅ Enhanced reward: {enhanced_reward:.4f}")
            except Exception as e:
                self.test_results[test_name]['details']['enhanced_reward'] = {
                    'status': 'failed',
                    'error': str(e)
                }
                logger.error(f"❌ Enhanced reward calculation failed: {e}")

            self.test_results[test_name]['status'] = 'completed'

        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Reinforcement learning test failed: {e}")

    async def _test_graph_neural_network(self):
        """Test graph neural network"""
        logger.info("🕸️ Testing Graph Neural Network...")

        test_name = "graph_neural_network"
        self.test_results[test_name] = {'status': 'running', 'details': {}}

        try:
            # Test GNN initialization and node creation
            try:
                from src.neural.graph_neural_network import MarketGraphNeuralNetwork, GraphConfig

                config = GraphConfig(
                    node_features=64,
                    hidden_dim=128,
                    num_layers=4,
                    num_heads=6,
                    dropout=0.1
                )

                gnn = MarketGraphNeuralNetwork(
                    config=config,
                    num_assets=5,
                    num_sentiment_nodes=5,
                    num_onchain_nodes=8,
                    num_macro_nodes=3
                )

                # Test sentiment node creation
                sentiment_nodes = gnn.create_sentiment_nodes(self.test_market_data, self.test_sentiment_data)

                # Test on-chain node creation
                onchain_nodes = gnn.create_onchain_nodes(self.test_market_data)

                self.test_results[test_name]['details']['gnn_functionality'] = {
                    'status': 'success',
                    'total_nodes': gnn.total_nodes,
                    'sentiment_nodes_shape': list(sentiment_nodes.shape),
                    'onchain_nodes_shape': list(onchain_nodes.shape)
                }
                logger.info(f"✅ GNN: {gnn.total_nodes} total nodes, "
                           f"sentiment: {sentiment_nodes.shape}, onchain: {onchain_nodes.shape}")
            except Exception as e:
                self.test_results[test_name]['details']['gnn_functionality'] = {
                    'status': 'failed',
                    'error': str(e)
                }
                logger.error(f"❌ GNN functionality test failed: {e}")

            self.test_results[test_name]['status'] = 'completed'

        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Graph neural network test failed: {e}")

    async def _test_system_integration(self):
        """Test system integration"""
        logger.info("🔗 Testing System Integration...")

        test_name = "system_integration"
        self.test_results[test_name] = {'status': 'running', 'details': {}}

        try:
            # Test end-to-end pipeline
            integration_success = True
            pipeline_results = {}

            # Step 1: Feature extraction
            if 'feature_engineer' in self.components:
                try:
                    feature_engineer = self.components['feature_engineer']
                    all_features = {}

                    fg_features = await feature_engineer.extract_fear_greed_features(self.test_market_data)
                    all_features.update(fg_features)

                    eth_features = await feature_engineer.extract_etherscan_features(self.test_market_data)
                    all_features.update(eth_features)

                    glass_features = await feature_engineer.extract_glassnode_features(self.test_market_data)
                    all_features.update(glass_features)

                    pipeline_results['feature_extraction'] = {
                        'status': 'success',
                        'total_features': len(all_features)
                    }
                    logger.info(f"✅ Pipeline Step 1: {len(all_features)} features extracted")
                except Exception as e:
                    pipeline_results['feature_extraction'] = {'status': 'failed', 'error': str(e)}
                    integration_success = False
                    logger.error(f"❌ Pipeline Step 1 failed: {e}")

            # Step 2: Sentiment analysis
            if 'sentiment_scorer' in self.components:
                try:
                    sentiment_scorer = self.components['sentiment_scorer']
                    sentiment_result = await sentiment_scorer.calculate_composite_sentiment(
                        self.test_market_data, self.test_sentiment_data
                    )

                    pipeline_results['sentiment_analysis'] = {
                        'status': 'success',
                        'composite_score': sentiment_result.composite_score,
                        'confidence': sentiment_result.confidence
                    }
                    logger.info(f"✅ Pipeline Step 2: Sentiment score {sentiment_result.composite_score:.3f}")
                except Exception as e:
                    pipeline_results['sentiment_analysis'] = {'status': 'failed', 'error': str(e)}
                    integration_success = False
                    logger.error(f"❌ Pipeline Step 2 failed: {e}")

            # Step 3: Regime detection
            if 'regime_detector' in self.components:
                try:
                    regime_detector = self.components['regime_detector']
                    regime_result = await regime_detector.detect_market_regime(
                        self.test_market_data, self.test_sentiment_data, 0.35
                    )

                    pipeline_results['regime_detection'] = {
                        'status': 'success',
                        'regime': regime_result.primary_regime.value,
                        'confidence': regime_result.regime_confidence
                    }
                    logger.info(f"✅ Pipeline Step 3: Regime {regime_result.primary_regime.value}")
                except Exception as e:
                    pipeline_results['regime_detection'] = {'status': 'failed', 'error': str(e)}
                    integration_success = False
                    logger.error(f"❌ Pipeline Step 3 failed: {e}")

            self.test_results[test_name]['details']['pipeline_results'] = pipeline_results
            self.test_results[test_name]['details']['integration_success'] = integration_success
            self.test_results[test_name]['status'] = 'completed'

            if integration_success:
                logger.info("✅ System integration test passed")
            else:
                logger.warning("⚠️ System integration test had failures")

        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ System integration test failed: {e}")

    async def _test_performance_metrics(self):
        """Test performance metrics and validation"""
        logger.info("📈 Testing Performance Metrics...")

        test_name = "performance_metrics"
        self.test_results[test_name] = {'status': 'running', 'details': {}}

        try:
            # Simulate multiple prediction cycles to test performance
            prediction_results = []

            for i in range(10):  # Run 10 test cycles
                cycle_results = {}

                # Test prediction accuracy simulation
                if 'profit_predictor' in self.components:
                    try:
                        profit_predictor = self.components['profit_predictor']
                        prediction = await profit_predictor.predict_profit(
                            self.test_market_data, 'BTC-USD', self.test_sentiment_data
                        )
                        cycle_results['profit_prediction'] = {
                            'score': prediction.profit_score,
                            'confidence': prediction.confidence
                        }
                    except Exception as e:
                        cycle_results['profit_prediction'] = {'error': str(e)}

                # Test sentiment consistency
                if 'sentiment_scorer' in self.components:
                    try:
                        sentiment_scorer = self.components['sentiment_scorer']
                        sentiment_result = await sentiment_scorer.calculate_composite_sentiment(
                            self.test_market_data, self.test_sentiment_data
                        )
                        cycle_results['sentiment'] = {
                            'score': sentiment_result.composite_score,
                            'confidence': sentiment_result.confidence
                        }
                    except Exception as e:
                        cycle_results['sentiment'] = {'error': str(e)}

                prediction_results.append(cycle_results)

            # Calculate performance metrics
            profit_predictions = [r.get('profit_prediction', {}).get('score', 0)
                                for r in prediction_results
                                if 'profit_prediction' in r and 'score' in r['profit_prediction']]

            sentiment_scores = [r.get('sentiment', {}).get('score', 0)
                              for r in prediction_results
                              if 'sentiment' in r and 'score' in r['sentiment']]

            if profit_predictions:
                import statistics
                profit_std = statistics.stdev(profit_predictions) if len(profit_predictions) > 1 else 0
                profit_mean = statistics.mean(profit_predictions)
            else:
                profit_std = 0
                profit_mean = 0

            if sentiment_scores:
                sentiment_std = statistics.stdev(sentiment_scores) if len(sentiment_scores) > 1 else 0
                sentiment_mean = statistics.mean(sentiment_scores)
            else:
                sentiment_std = 0
                sentiment_mean = 0

            self.test_results[test_name]['details']['performance_metrics'] = {
                'prediction_cycles': len(prediction_results),
                'profit_prediction_consistency': 1.0 - min(1.0, profit_std),
                'sentiment_consistency': 1.0 - min(1.0, sentiment_std),
                'profit_mean': profit_mean,
                'sentiment_mean': sentiment_mean,
                'successful_cycles': len([r for r in prediction_results if 'profit_prediction' in r])
            }

            self.test_results[test_name]['status'] = 'completed'
            logger.info(f"✅ Performance metrics: {len(prediction_results)} cycles completed")

        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Performance metrics test failed: {e}")

    def _generate_test_report(self):
        """Generate comprehensive test report"""
        logger.info("📋 Generating Test Report...")

        report = {
            'test_summary': {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'total_tests': len(self.test_results),
                'passed_tests': len([t for t in self.test_results.values() if t.get('status') == 'completed']),
                'failed_tests': len([t for t in self.test_results.values() if t.get('status') == 'failed']),
                'skipped_tests': len([t for t in self.test_results.values() if t.get('status') == 'skipped'])
            },
            'detailed_results': self.test_results
        }

        # Save report to file
        try:
            report_path = 'tests/neural_system_test_report.json'
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            logger.info(f"📋 Test report saved to {report_path}")
        except Exception as e:
            logger.error(f"❌ Failed to save test report: {e}")

        # Print summary
        summary = report['test_summary']
        logger.info(f"🧪 TEST SUMMARY:")
        logger.info(f"   Total Tests: {summary['total_tests']}")
        logger.info(f"   Passed: {summary['passed_tests']}")
        logger.info(f"   Failed: {summary['failed_tests']}")
        logger.info(f"   Skipped: {summary['skipped_tests']}")

        success_rate = summary['passed_tests'] / summary['total_tests'] if summary['total_tests'] > 0 else 0
        logger.info(f"   Success Rate: {success_rate:.1%}")

        return report

async def main():
    """Run comprehensive neural system tests"""
    print("🧪 Enhanced Neural Trading System - Comprehensive Testing")
    print("=" * 60)

    tester = EnhancedNeuralSystemTester()
    results = await tester.run_comprehensive_tests()

    if 'error' in results:
        print(f"❌ Testing failed: {results['error']}")
        return False

    # Check overall success
    passed_tests = len([t for t in results.values() if t.get('status') == 'completed'])
    total_tests = len(results)
    success_rate = passed_tests / total_tests if total_tests > 0 else 0

    print(f"\n🎯 FINAL RESULT: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")

    if success_rate >= 0.8:  # 80% success rate threshold
        print("✅ Enhanced Neural System Testing: PASSED")
        return True
    else:
        print("❌ Enhanced Neural System Testing: FAILED")
        return False

if __name__ == "__main__":
    asyncio.run(main())
