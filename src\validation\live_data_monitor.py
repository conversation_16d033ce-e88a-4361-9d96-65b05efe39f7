#!/usr/bin/env python3
"""
Live Data Monitor - Real-time monitoring for non-live data usage detection
Continuously monitors all data sources and alerts on any non-live data attempts
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

@dataclass
class DataSourceAlert:
    """Alert for non-live data usage"""
    timestamp: datetime
    alert_type: str
    severity: str  # CRITICAL, HIGH, MEDIUM, LOW
    source: str
    description: str
    data_sample: str
    action_taken: str

@dataclass
class DataSourceMetrics:
    """Metrics for a data source"""
    source_name: str
    total_requests: int
    live_requests: int
    non_live_requests: int
    last_request_time: Optional[datetime]
    average_latency_ms: float
    error_count: int
    uptime_percentage: float

class LiveDataMonitor:
    """Real-time monitor for live data usage compliance"""
    
    def __init__(self):
        self.monitoring_active = False
        self.alerts: deque = deque(maxlen=1000)  # Keep last 1000 alerts
        self.metrics: Dict[str, DataSourceMetrics] = {}
        self.monitored_sources: Set[str] = set()
        self.alert_callbacks: List[callable] = []
        
        # Monitoring configuration
        self.check_interval = 1.0  # Check every second
        self.alert_threshold = 5  # Alert after 5 consecutive violations
        self.violation_counts: Dict[str, int] = defaultdict(int)
        
        # Non-live data indicators
        self.non_live_indicators = {
            'mock_data_patterns': [
                'mock', 'fake', 'dummy', 'test', 'sample', 'placeholder',
                'synthetic', 'simulated', 'hardcoded', 'fallback', 'cached'
            ],
            'test_endpoints': [
                'testnet', 'sandbox', 'demo', 'test.api', 'staging',
                'localhost', '127.0.0.1', 'mock.api'
            ],
            'simulation_flags': [
                'simulation_mode', 'test_mode', 'demo_mode', 'paper_trading',
                'dry_run', 'virtual_trading', 'fake_trading'
            ]
        }

    async def start_monitoring(self):
        """Start real-time monitoring"""
        if self.monitoring_active:
            logger.warning("⚠️ [MONITOR] Monitoring already active")
            return
        
        self.monitoring_active = True
        logger.info("🔍 [MONITOR] Starting live data monitoring...")
        
        # Start monitoring tasks
        monitoring_tasks = [
            asyncio.create_task(self._monitor_data_sources()),
            asyncio.create_task(self._monitor_environment_variables()),
            asyncio.create_task(self._monitor_api_calls()),
            asyncio.create_task(self._generate_periodic_reports())
        ]
        
        try:
            await asyncio.gather(*monitoring_tasks)
        except Exception as e:
            logger.error(f"❌ [MONITOR] Monitoring failed: {e}")
            self.monitoring_active = False
            raise

    async def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring_active = False
        logger.info("🛑 [MONITOR] Stopping live data monitoring")

    async def _monitor_data_sources(self):
        """Monitor data sources for non-live data usage"""
        while self.monitoring_active:
            try:
                # Check all registered data sources
                for source_name in self.monitored_sources:
                    await self._check_data_source(source_name)
                
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"❌ [MONITOR] Data source monitoring error: {e}")
                await asyncio.sleep(5)  # Wait before retrying

    async def _monitor_environment_variables(self):
        """Monitor environment variables for simulation flags"""
        while self.monitoring_active:
            try:
                import os
                
                for flag in self.non_live_indicators['simulation_flags']:
                    value = os.getenv(flag.upper())
                    if value and value.lower() in ['true', '1', 'yes', 'on']:
                        await self._create_alert(
                            alert_type='environment_variable_violation',
                            severity='CRITICAL',
                            source='environment',
                            description=f'Simulation flag enabled: {flag.upper()}={value}',
                            data_sample=f'{flag.upper()}={value}',
                            action_taken='SYSTEM_TERMINATION_REQUIRED'
                        )
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"❌ [MONITOR] Environment monitoring error: {e}")
                await asyncio.sleep(30)

    async def _monitor_api_calls(self):
        """Monitor API calls for non-live endpoints"""
        while self.monitoring_active:
            try:
                # This would integrate with HTTP client monitoring
                # For now, we'll check for known patterns
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"❌ [MONITOR] API call monitoring error: {e}")
                await asyncio.sleep(10)

    async def _generate_periodic_reports(self):
        """Generate periodic monitoring reports"""
        while self.monitoring_active:
            try:
                await asyncio.sleep(300)  # Every 5 minutes
                
                report = self._generate_monitoring_report()
                logger.info(f"📊 [MONITOR] {report}")
                
            except Exception as e:
                logger.error(f"❌ [MONITOR] Report generation error: {e}")

    async def _check_data_source(self, source_name: str):
        """Check a specific data source for compliance"""
        try:
            # Get metrics for this source
            if source_name not in self.metrics:
                self.metrics[source_name] = DataSourceMetrics(
                    source_name=source_name,
                    total_requests=0,
                    live_requests=0,
                    non_live_requests=0,
                    last_request_time=None,
                    average_latency_ms=0.0,
                    error_count=0,
                    uptime_percentage=100.0
                )
            
            metrics = self.metrics[source_name]
            
            # Check for non-live data indicators
            # This would be implemented based on specific data source APIs
            
        except Exception as e:
            logger.debug(f"Error checking data source {source_name}: {e}")

    async def _create_alert(self, alert_type: str, severity: str, source: str, 
                          description: str, data_sample: str, action_taken: str):
        """Create and process an alert"""
        alert = DataSourceAlert(
            timestamp=datetime.now(),
            alert_type=alert_type,
            severity=severity,
            source=source,
            description=description,
            data_sample=data_sample,
            action_taken=action_taken
        )
        
        self.alerts.append(alert)
        
        # Log alert
        logger.error(f"🚨 [ALERT] {severity} - {description}")
        logger.error(f"🚨 [ALERT] Source: {source}")
        logger.error(f"🚨 [ALERT] Data: {data_sample}")
        logger.error(f"🚨 [ALERT] Action: {action_taken}")
        
        # Update violation count
        self.violation_counts[source] += 1
        
        # Take action based on severity
        if severity == 'CRITICAL':
            await self._handle_critical_alert(alert)
        elif severity == 'HIGH':
            await self._handle_high_alert(alert)
        
        # Notify callbacks
        for callback in self.alert_callbacks:
            try:
                await callback(alert)
            except Exception as e:
                logger.error(f"❌ [ALERT] Callback error: {e}")

    async def _handle_critical_alert(self, alert: DataSourceAlert):
        """Handle critical alerts - terminate system"""
        logger.error(f"💀 [CRITICAL] TERMINATING SYSTEM - {alert.description}")
        
        # Save alert to file for audit
        await self._save_alert_to_audit_log(alert)
        
        # Terminate system to prevent non-live trading
        raise RuntimeError(f"CRITICAL ALERT: {alert.description} - System terminated to prevent non-live trading")

    async def _handle_high_alert(self, alert: DataSourceAlert):
        """Handle high priority alerts"""
        logger.error(f"⚠️ [HIGH] {alert.description}")
        
        # Check if we've exceeded threshold
        if self.violation_counts[alert.source] >= self.alert_threshold:
            await self._handle_critical_alert(alert)

    async def _save_alert_to_audit_log(self, alert: DataSourceAlert):
        """Save alert to audit log file"""
        try:
            from pathlib import Path
            
            audit_dir = Path("logs/audit")
            audit_dir.mkdir(parents=True, exist_ok=True)
            
            audit_file = audit_dir / f"live_data_alerts_{datetime.now().strftime('%Y%m%d')}.json"
            
            alert_data = asdict(alert)
            alert_data['timestamp'] = alert.timestamp.isoformat()
            
            # Append to file
            alerts_data = []
            if audit_file.exists():
                with open(audit_file, 'r') as f:
                    alerts_data = json.load(f)
            
            alerts_data.append(alert_data)
            
            with open(audit_file, 'w') as f:
                json.dump(alerts_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"❌ [AUDIT] Failed to save alert: {e}")

    def register_data_source(self, source_name: str):
        """Register a data source for monitoring"""
        self.monitored_sources.add(source_name)
        logger.info(f"📝 [MONITOR] Registered data source: {source_name}")

    def unregister_data_source(self, source_name: str):
        """Unregister a data source"""
        self.monitored_sources.discard(source_name)
        logger.info(f"📝 [MONITOR] Unregistered data source: {source_name}")

    def add_alert_callback(self, callback: callable):
        """Add callback for alert notifications"""
        self.alert_callbacks.append(callback)

    def record_data_request(self, source_name: str, is_live: bool, latency_ms: float = 0):
        """Record a data request for metrics"""
        if source_name not in self.metrics:
            self.metrics[source_name] = DataSourceMetrics(
                source_name=source_name,
                total_requests=0,
                live_requests=0,
                non_live_requests=0,
                last_request_time=None,
                average_latency_ms=0.0,
                error_count=0,
                uptime_percentage=100.0
            )
        
        metrics = self.metrics[source_name]
        metrics.total_requests += 1
        metrics.last_request_time = datetime.now()
        
        if is_live:
            metrics.live_requests += 1
        else:
            metrics.non_live_requests += 1
            
            # Create alert for non-live data
            asyncio.create_task(self._create_alert(
                alert_type='non_live_data_request',
                severity='HIGH',
                source=source_name,
                description=f'Non-live data request detected from {source_name}',
                data_sample=f'source={source_name}, live={is_live}',
                action_taken='LOGGED_AND_MONITORED'
            ))
        
        # Update average latency
        if latency_ms > 0:
            total_latency = metrics.average_latency_ms * (metrics.total_requests - 1)
            metrics.average_latency_ms = (total_latency + latency_ms) / metrics.total_requests

    def _generate_monitoring_report(self) -> str:
        """Generate monitoring report"""
        total_sources = len(self.monitored_sources)
        total_alerts = len(self.alerts)
        critical_alerts = len([a for a in self.alerts if a.severity == 'CRITICAL'])
        high_alerts = len([a for a in self.alerts if a.severity == 'HIGH'])
        
        # Calculate compliance rate
        total_requests = sum(m.total_requests for m in self.metrics.values())
        live_requests = sum(m.live_requests for m in self.metrics.values())
        compliance_rate = (live_requests / total_requests * 100) if total_requests > 0 else 100
        
        return (
            f"Live Data Monitoring Report - "
            f"Sources: {total_sources}, "
            f"Alerts: {total_alerts} (Critical: {critical_alerts}, High: {high_alerts}), "
            f"Compliance: {compliance_rate:.1f}%"
        )

    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get comprehensive monitoring summary"""
        recent_alerts = list(self.alerts)[-10:]  # Last 10 alerts
        
        return {
            'monitoring_active': self.monitoring_active,
            'monitored_sources': list(self.monitored_sources),
            'total_alerts': len(self.alerts),
            'recent_alerts': [asdict(alert) for alert in recent_alerts],
            'metrics': {name: asdict(metrics) for name, metrics in self.metrics.items()},
            'violation_counts': dict(self.violation_counts),
            'compliance_summary': self._generate_monitoring_report()
        }

# Global monitor instance
live_data_monitor = LiveDataMonitor()
