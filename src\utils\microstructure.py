#!/usr/bin/env python3
"""
Market Microstructure Analysis Module
Provides order book analysis, spread calculation, and market imbalance detection
"""

import asyncio
import logging
import time
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
from dataclasses import dataclass
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

@dataclass
class OrderBookMetrics:
    """Order book analysis metrics"""
    symbol: str
    spread: float
    spread_pct: float
    imbalance: float
    bid_depth: float
    ask_depth: float
    liquidity_score: float
    timestamp: float

class OrderBookAnalyzer:
    """
    Advanced order book analyzer for market microstructure analysis
    Provides real-time spread and imbalance calculations
    """
    
    def __init__(self, depth: int = 10, cache_ttl: int = 1):
        """
        Initialize OrderBookAnalyzer
        
        Args:
            depth: Number of order book levels to analyze
            cache_ttl: Cache time-to-live in seconds
        """
        self.depth = depth
        self.cache_ttl = cache_ttl
        self.metrics_cache = {}
        self.order_book_cache = {}
        self.spread_history = defaultdict(lambda: deque(maxlen=100))
        self.imbalance_history = defaultdict(lambda: deque(maxlen=100))
        
        logger.info(f"🔬 [MICROSTRUCTURE] OrderBookAnalyzer initialized with depth={depth}")
    
    async def get_spreads(self, symbol: str) -> np.ndarray:
        """
        Get historical spreads for a symbol
        
        Args:
            symbol: Trading symbol
            
        Returns:
            numpy array of historical spreads
        """
        try:
            # Get current metrics
            metrics = await self._get_order_book_metrics(symbol)
            
            # Update spread history
            if metrics:
                self.spread_history[symbol].append(metrics.spread)
            
            # Return as numpy array
            spreads = list(self.spread_history[symbol])
            if not spreads:
                # Return default spread array if no data
                return np.array([0.001] * 10)
            
            # Pad with last value if insufficient data
            while len(spreads) < 10:
                spreads.append(spreads[-1] if spreads else 0.001)
            
            return np.array(spreads[-10:])  # Return last 10 spreads
            
        except Exception as e:
            logger.error(f"Error getting spreads for {symbol}: {e}")
            return np.array([0.001] * 10)
    
    async def get_imbalance(self, symbol: str) -> float:
        """
        Get current order book imbalance for a symbol
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Order book imbalance (-1 to 1, negative = ask heavy, positive = bid heavy)
        """
        try:
            # Get current metrics
            metrics = await self._get_order_book_metrics(symbol)
            
            if metrics:
                # Update imbalance history
                self.imbalance_history[symbol].append(metrics.imbalance)
                return metrics.imbalance
            
            # Return neutral imbalance if no data
            return 0.0
            
        except Exception as e:
            logger.error(f"Error getting imbalance for {symbol}: {e}")
            return 0.0
    
    async def _get_order_book_metrics(self, symbol: str) -> Optional[OrderBookMetrics]:
        """
        Get comprehensive order book metrics for a symbol
        
        Args:
            symbol: Trading symbol
            
        Returns:
            OrderBookMetrics object or None if unavailable
        """
        try:
            # Check cache first
            cache_key = f"metrics_{symbol}"
            current_time = time.time()
            
            if cache_key in self.metrics_cache:
                cached_data = self.metrics_cache[cache_key]
                if current_time - cached_data['timestamp'] < self.cache_ttl:
                    return cached_data['metrics']
            
            # Fetch fresh order book data
            order_book = await self._fetch_order_book(symbol)
            
            if not order_book:
                return None
            
            # Calculate metrics
            metrics = self._calculate_metrics(symbol, order_book)
            
            # Cache results
            self.metrics_cache[cache_key] = {
                'metrics': metrics,
                'timestamp': current_time
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting order book metrics for {symbol}: {e}")
            return None

    async def _fetch_order_book(self, symbol: str) -> Optional[Dict]:
        """
        Fetch order book data for a symbol

        Args:
            symbol: Trading symbol

        Returns:
            Order book data or None if unavailable
        """
        try:
            # Check if we have cached order book data
            cache_key = f"orderbook_{symbol}"
            current_time = time.time()

            if cache_key in self.order_book_cache:
                cached_data = self.order_book_cache[cache_key]
                if current_time - cached_data['timestamp'] < self.cache_ttl:
                    return cached_data['data']

            # In a real implementation, this would fetch from exchange API
            # For now, we'll simulate order book data or try to get from global state
            order_book = await self._get_simulated_order_book(symbol)

            # Cache the data
            if order_book:
                self.order_book_cache[cache_key] = {
                    'data': order_book,
                    'timestamp': current_time
                }

            return order_book

        except Exception as e:
            logger.error(f"Error fetching order book for {symbol}: {e}")
            return None

    async def _get_simulated_order_book(self, symbol: str) -> Dict:
        """
        Generate simulated order book data for testing
        In production, this would be replaced with real exchange API calls
        """
        try:
            # Try to get real data from exchange if available
            # This is a fallback simulation for when real data isn't available

            # Generate realistic bid/ask spreads based on symbol
            base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 1.0
            spread_pct = 0.001  # 0.1% spread

            mid_price = base_price
            spread = mid_price * spread_pct

            # Generate order book levels
            bids = []
            asks = []

            for i in range(self.depth):
                # Bids (decreasing prices)
                bid_price = mid_price - spread/2 - (i * spread * 0.1)
                bid_volume = np.random.uniform(0.1, 10.0)
                bids.append([bid_price, bid_volume])

                # Asks (increasing prices)
                ask_price = mid_price + spread/2 + (i * spread * 0.1)
                ask_volume = np.random.uniform(0.1, 10.0)
                asks.append([ask_price, ask_volume])

            return {
                'bids': bids,
                'asks': asks,
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"Error generating simulated order book for {symbol}: {e}")
            return {
                'bids': [[1.0, 1.0]],
                'asks': [[1.001, 1.0]],
                'timestamp': time.time()
            }

    def _calculate_metrics(self, symbol: str, order_book: Dict) -> OrderBookMetrics:
        """
        Calculate comprehensive order book metrics

        Args:
            symbol: Trading symbol
            order_book: Order book data

        Returns:
            OrderBookMetrics object
        """
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])

            if not bids or not asks:
                return OrderBookMetrics(
                    symbol=symbol,
                    spread=0.001,
                    spread_pct=0.1,
                    imbalance=0.0,
                    bid_depth=0.0,
                    ask_depth=0.0,
                    liquidity_score=0.0,
                    timestamp=time.time()
                )

            # Get best bid and ask
            best_bid = float(bids[0][0])
            best_ask = float(asks[0][0])

            # Calculate spread
            spread = best_ask - best_bid
            mid_price = (best_bid + best_ask) / 2
            spread_pct = (spread / mid_price) * 100 if mid_price > 0 else 0.1

            # Calculate depth (sum of volumes at top levels)
            bid_depth = sum(float(bid[1]) for bid in bids[:self.depth])
            ask_depth = sum(float(ask[1]) for ask in asks[:self.depth])

            # Calculate imbalance
            total_depth = bid_depth + ask_depth
            imbalance = (bid_depth - ask_depth) / total_depth if total_depth > 0 else 0.0

            # Calculate liquidity score
            liquidity_score = min(1.0, total_depth / 1000.0)  # Normalize to 0-1

            return OrderBookMetrics(
                symbol=symbol,
                spread=spread,
                spread_pct=spread_pct,
                imbalance=imbalance,
                bid_depth=bid_depth,
                ask_depth=ask_depth,
                liquidity_score=liquidity_score,
                timestamp=time.time()
            )

        except Exception as e:
            logger.error(f"Error calculating metrics for {symbol}: {e}")
            return OrderBookMetrics(
                symbol=symbol,
                spread=0.001,
                spread_pct=0.1,
                imbalance=0.0,
                bid_depth=0.0,
                ask_depth=0.0,
                liquidity_score=0.0,
                timestamp=time.time()
            )

    def get_spread_statistics(self, symbol: str) -> Dict[str, float]:
        """
        Get spread statistics for a symbol

        Args:
            symbol: Trading symbol

        Returns:
            Dictionary with spread statistics
        """
        try:
            spreads = list(self.spread_history[symbol])
            if not spreads:
                return {
                    'mean': 0.001,
                    'std': 0.0,
                    'min': 0.001,
                    'max': 0.001,
                    'count': 0
                }

            spreads_array = np.array(spreads)
            return {
                'mean': float(np.mean(spreads_array)),
                'std': float(np.std(spreads_array)),
                'min': float(np.min(spreads_array)),
                'max': float(np.max(spreads_array)),
                'count': len(spreads)
            }

        except Exception as e:
            logger.error(f"Error getting spread statistics for {symbol}: {e}")
            return {
                'mean': 0.001,
                'std': 0.0,
                'min': 0.001,
                'max': 0.001,
                'count': 0
            }

    def get_imbalance_statistics(self, symbol: str) -> Dict[str, float]:
        """
        Get imbalance statistics for a symbol

        Args:
            symbol: Trading symbol

        Returns:
            Dictionary with imbalance statistics
        """
        try:
            imbalances = list(self.imbalance_history[symbol])
            if not imbalances:
                return {
                    'mean': 0.0,
                    'std': 0.0,
                    'min': 0.0,
                    'max': 0.0,
                    'count': 0
                }

            imbalances_array = np.array(imbalances)
            return {
                'mean': float(np.mean(imbalances_array)),
                'std': float(np.std(imbalances_array)),
                'min': float(np.min(imbalances_array)),
                'max': float(np.max(imbalances_array)),
                'count': len(imbalances)
            }

        except Exception as e:
            logger.error(f"Error getting imbalance statistics for {symbol}: {e}")
            return {
                'mean': 0.0,
                'std': 0.0,
                'min': 0.0,
                'max': 0.0,
                'count': 0
            }

    def clear_cache(self, symbol: Optional[str] = None):
        """
        Clear cached data

        Args:
            symbol: Specific symbol to clear, or None to clear all
        """
        try:
            if symbol:
                # Clear specific symbol
                keys_to_remove = [k for k in self.metrics_cache.keys() if symbol in k]
                for key in keys_to_remove:
                    del self.metrics_cache[key]

                keys_to_remove = [k for k in self.order_book_cache.keys() if symbol in k]
                for key in keys_to_remove:
                    del self.order_book_cache[key]

                if symbol in self.spread_history:
                    self.spread_history[symbol].clear()
                if symbol in self.imbalance_history:
                    self.imbalance_history[symbol].clear()
            else:
                # Clear all
                self.metrics_cache.clear()
                self.order_book_cache.clear()
                self.spread_history.clear()
                self.imbalance_history.clear()

            logger.info(f"🧹 [MICROSTRUCTURE] Cache cleared for {symbol or 'all symbols'}")

        except Exception as e:
            logger.error(f"Error clearing cache: {e}")

# Export the main class
__all__ = ['OrderBookAnalyzer', 'OrderBookMetrics']
