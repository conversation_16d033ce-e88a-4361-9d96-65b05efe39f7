#!/usr/bin/env python3
"""
Test Strategic Profit-Driven Diversification

This script tests the enhanced diversification system that:
1. Selects trading pairs based on profit potential and strategic analysis
2. Applies intelligent diversification rules while maintaining profit focus
3. Creates strategic opportunities with calculated confidence and risk scores
4. Ensures the system trades for profit, not just random diversification
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - %(name)s - %(message)s'
)
logger = logging.getLogger("StrategicDiversificationTest")

async def test_strategic_profit_selection():
    """Test the strategic profit-driven target selection"""
    logger.info("🧪 [TEST-1] Testing Strategic Profit Target Selection...")
    
    try:
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Initialize trading engine
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not api_key or not api_secret:
            logger.error("❌ [TEST-1] Bybit credentials not found")
            return False
        
        bybit_client = BybitClientFixed(api_key, api_secret)
        exchange_clients = {'bybit': bybit_client}
        
        trading_engine = MultiCurrencyTradingEngine(
            exchange_clients=exchange_clients,
            config={'strategic_diversification': True}
        )
        
        # Test strategic selection
        candidate_currencies = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'AVAX', 'UNI', 'MATIC', 'ALGO']
        current_balances = {'USDT': 100.0, 'BTC': 0.001}
        
        strategic_targets = await trading_engine._select_strategic_profit_targets(
            candidate_currencies, 'bybit', current_balances
        )
        
        logger.info(f"🎯 [TEST-1] Strategic targets selected: {len(strategic_targets)}")
        logger.info(f"🎯 [TEST-1] Targets: {strategic_targets}")
        
        if len(strategic_targets) > 0:
            logger.info("✅ [TEST-1] Strategic profit selection WORKING")
            return True
        else:
            logger.warning("⚠️ [TEST-1] No strategic targets selected")
            return False
            
    except Exception as e:
        logger.error(f"❌ [TEST-1] Strategic profit selection test failed: {e}")
        return False

async def test_strategic_diversification_rules():
    """Test the strategic diversification rules application"""
    logger.info("🧪 [TEST-2] Testing Strategic Diversification Rules...")
    
    try:
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Initialize trading engine
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        bybit_client = BybitClientFixed(api_key, api_secret)
        exchange_clients = {'bybit': bybit_client}
        
        trading_engine = MultiCurrencyTradingEngine(
            exchange_clients=exchange_clients,
            config={'strategic_diversification': True}
        )
        
        # Test diversification rules
        strategic_targets = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'AVAX', 'UNI']
        current_holdings = {'BTC', 'USDT'}
        
        diversified_targets = await trading_engine._apply_strategic_diversification_rules(
            strategic_targets, current_holdings, 'bybit'
        )
        
        logger.info(f"🎯 [TEST-2] Diversified targets: {len(diversified_targets)}")
        logger.info(f"🎯 [TEST-2] Targets: {diversified_targets}")
        
        # Check if diversification rules are applied correctly
        unowned_count = len([t for t in diversified_targets if t not in current_holdings])
        owned_count = len([t for t in diversified_targets if t in current_holdings])
        
        logger.info(f"🎯 [TEST-2] Unowned targets: {unowned_count}, Owned targets: {owned_count}")
        
        if unowned_count > owned_count:  # Should prioritize unowned for diversification
            logger.info("✅ [TEST-2] Strategic diversification rules WORKING")
            return True
        else:
            logger.warning("⚠️ [TEST-2] Diversification rules may need adjustment")
            return False
            
    except Exception as e:
        logger.error(f"❌ [TEST-2] Strategic diversification rules test failed: {e}")
        return False

async def test_strategic_opportunity_creation():
    """Test strategic opportunity creation with profit analysis"""
    logger.info("🧪 [TEST-3] Testing Strategic Opportunity Creation...")
    
    try:
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Initialize trading engine
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        bybit_client = BybitClientFixed(api_key, api_secret)
        exchange_clients = {'bybit': bybit_client}
        
        trading_engine = MultiCurrencyTradingEngine(
            exchange_clients=exchange_clients,
            config={'strategic_diversification': True}
        )
        
        # Test strategic opportunity creation
        target_currency = 'ETH'
        usdt_balance = 50.0
        
        opportunity = await trading_engine._create_diversified_buy_opportunity(
            'bybit', target_currency, usdt_balance
        )
        
        if opportunity:
            logger.info(f"🎯 [TEST-3] Strategic opportunity created:")
            logger.info(f"    💰 Currency: {opportunity.pair.base}")
            logger.info(f"    💵 Amount: ${float(opportunity.amount):.2f}")
            logger.info(f"    📊 Confidence: {opportunity.confidence:.3f}")
            logger.info(f"    💹 Expected Profit: ${float(opportunity.expected_profit):.2f}")
            logger.info(f"    🎯 Strategy: {opportunity.strategy}")
            logger.info(f"    ⚡ Priority: {opportunity.execution_priority}")
            logger.info(f"    ⚠️ Risk Score: {opportunity.risk_score:.3f}")
            
            # Verify strategic elements
            is_strategic = (
                opportunity.strategy == 'strategic_diversified_buying' and
                opportunity.confidence > 0.6 and
                float(opportunity.expected_profit) > 0
            )
            
            if is_strategic:
                logger.info("✅ [TEST-3] Strategic opportunity creation WORKING")
                return True
            else:
                logger.warning("⚠️ [TEST-3] Opportunity lacks strategic elements")
                return False
        else:
            logger.warning("⚠️ [TEST-3] No opportunity created")
            return False
            
    except Exception as e:
        logger.error(f"❌ [TEST-3] Strategic opportunity creation test failed: {e}")
        return False

async def test_strategic_score_calculation():
    """Test strategic score calculation"""
    logger.info("🧪 [TEST-4] Testing Strategic Score Calculation...")
    
    try:
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Initialize trading engine
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        bybit_client = BybitClientFixed(api_key, api_secret)
        exchange_clients = {'bybit': bybit_client}
        
        trading_engine = MultiCurrencyTradingEngine(
            exchange_clients=exchange_clients,
            config={'strategic_diversification': True}
        )
        
        # Test strategic score calculation
        currency = 'ETH'
        profit_prediction = {'predicted_profit': 0.05}  # 5% profit prediction
        market_data = {
            'change_24h': 0.03,  # 3% change
            'volume_change': 0.2,  # 20% volume change
            'volatility': 0.25,  # 25% volatility
            'volume': 5000000,  # 5M volume
            'market_cap_rank': 2  # ETH is rank 2
        }
        current_balances = {'USDT': 100.0}
        
        strategic_score = await trading_engine._calculate_strategic_score(
            currency, profit_prediction, market_data, current_balances
        )
        
        logger.info(f"🎯 [TEST-4] Strategic score for {currency}: {strategic_score:.3f}")
        
        if 0.5 <= strategic_score <= 1.0:  # Should be reasonable score
            logger.info("✅ [TEST-4] Strategic score calculation WORKING")
            return True
        else:
            logger.warning(f"⚠️ [TEST-4] Strategic score out of expected range: {strategic_score}")
            return False
            
    except Exception as e:
        logger.error(f"❌ [TEST-4] Strategic score calculation test failed: {e}")
        return False

async def main():
    """Run all strategic diversification tests"""
    logger.info("🚀 [MAIN] Starting Strategic Profit-Driven Diversification Tests...")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    test_results = {}
    
    # Test 1: Strategic Profit Selection
    test_results['strategic_profit_selection'] = await test_strategic_profit_selection()
    
    # Test 2: Strategic Diversification Rules
    test_results['strategic_diversification_rules'] = await test_strategic_diversification_rules()
    
    # Test 3: Strategic Opportunity Creation
    test_results['strategic_opportunity_creation'] = await test_strategic_opportunity_creation()
    
    # Test 4: Strategic Score Calculation
    test_results['strategic_score_calculation'] = await test_strategic_score_calculation()
    
    # Summary
    logger.info("\n" + "="*70)
    logger.info("📊 [SUMMARY] Strategic Diversification Test Results:")
    logger.info("="*70)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
    
    total_passed = sum(test_results.values())
    total_tests = len(test_results)
    
    logger.info(f"\n📊 [SUMMARY] {total_passed}/{total_tests} tests passed")
    
    if total_passed >= 3:  # At least 3 out of 4 should pass
        logger.info("🎉 [SUCCESS] Strategic Profit-Driven Diversification is WORKING!")
        logger.info("🎯 [STRATEGIC-BENEFITS]:")
        logger.info("   ✅ Pairs selected based on profit potential and market analysis")
        logger.info("   ✅ Diversification rules applied while maintaining profit focus")
        logger.info("   ✅ Strategic opportunities with calculated confidence and risk")
        logger.info("   ✅ Intelligent position sizing based on strategic scores")
        logger.info("   ✅ Correlation-based diversification to reduce portfolio risk")
        return True
    else:
        logger.error("❌ [FAILURE] Strategic diversification needs improvement")
        return False

if __name__ == "__main__":
    asyncio.run(main())
