#!/usr/bin/env python3
"""
Test Price Fix - Verify that get_fast_price is working
"""

import os
import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Force Bybit-only environment
os.environ["BYBIT_ONLY_MODE"] = "true"
os.environ["COINBASE_ENABLED"] = "false"
os.environ["LIVE_TRADING"] = "true"
os.environ["REAL_MONEY_TRADING"] = "true"

async def test_price_fix():
    """Test the price fetching fix"""
    print("🧪 [TEST] Testing price fetching fix...")
    
    try:
        # Import Bybit client
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Get API credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            print("❌ [TEST] Missing API credentials")
            return False
        
        print("🔧 [TEST] Initializing Bybit client...")
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        
        if not bybit_client.session:
            print("❌ [TEST] Failed to initialize Bybit client")
            return False
        
        print("✅ [TEST] Bybit client initialized successfully")
        
        # Test get_fast_price method
        print("\n🧪 [TEST] Testing get_fast_price method...")
        
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
        
        for symbol in test_symbols:
            try:
                print(f"🔍 [TEST] Getting price for {symbol}...")
                price = await bybit_client.get_fast_price(symbol)
                
                if price and price > 0:
                    print(f"✅ [TEST] {symbol}: ${price:.4f}")
                else:
                    print(f"❌ [TEST] {symbol}: Invalid price: {price}")
                    return False
                    
            except Exception as e:
                print(f"❌ [TEST] Error getting price for {symbol}: {e}")
                return False
        
        # Test balance fetching
        print("\n🧪 [TEST] Testing balance fetching...")
        try:
            balances = await bybit_client.get_all_available_balances()
            usdt_balance = balances.get('USDT', 0)
            print(f"✅ [TEST] USDT Balance: ${usdt_balance:.2f}")
            
            if usdt_balance >= 5.0:
                print("✅ [TEST] Sufficient balance for trading")
                return True
            else:
                print(f"⚠️ [TEST] Low balance: ${usdt_balance:.2f} < $5.00")
                return True  # Still a success, just low balance
                
        except Exception as e:
            print(f"❌ [TEST] Error getting balance: {e}")
            return False
        
    except Exception as e:
        print(f"❌ [TEST] Critical error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 [TEST-PRICE] Testing price fetching fix...")
    
    try:
        success = asyncio.run(test_price_fix())
        if success:
            print("\n✅ [RESULT] Price fetching fix verified successfully!")
            print("🚀 [RESULT] Ready for aggressive trading!")
            sys.exit(0)
        else:
            print("\n❌ [RESULT] Price fetching needs attention")
            sys.exit(1)
    except Exception as e:
        print(f"❌ [TEST-PRICE] Testing failed: {e}")
        sys.exit(1)
