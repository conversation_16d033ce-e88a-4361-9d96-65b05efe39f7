#!/usr/bin/env python3
"""
TIME-AWARE TRADING STRATEGY SYSTEM
Implements time decay factors and execution urgency scoring
"""

import asyncio
import time
import logging
import math
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from ..performance.speed_optimizer import (
    fast_strategy_evaluation, fast_signal_generation, 
    cached_market_data, speed_optimizer
)

logger = logging.getLogger(__name__)

class UrgencyLevel(Enum):
    """Execution urgency levels"""
    CRITICAL = "critical"    # Execute immediately
    HIGH = "high"           # Execute within 5 seconds
    MEDIUM = "medium"       # Execute within 15 seconds
    LOW = "low"            # Execute within 30 seconds
    EXPIRED = "expired"     # Opportunity expired

@dataclass
class TimeAwareOpportunity:
    """Trading opportunity with time-aware scoring"""
    symbol: str
    action: str  # 'buy' or 'sell'
    base_profit_score: float
    confidence: float
    created_at: datetime
    expires_at: datetime
    market_volatility: float
    execution_complexity: float
    
    # Time-aware fields
    time_decay_factor: float = field(init=False)
    urgency_level: UrgencyLevel = field(init=False)
    time_weighted_score: float = field(init=False)
    execution_priority: int = field(init=False)
    
    def __post_init__(self):
        self.update_time_factors()
    
    def update_time_factors(self):
        """Update time-dependent factors"""
        current_time = datetime.now()
        
        # Calculate time decay (opportunities lose value over time)
        age_seconds = (current_time - self.created_at).total_seconds()
        time_to_expiry = (self.expires_at - current_time).total_seconds()
        
        # Exponential decay based on age and volatility
        decay_rate = 0.1 + (self.market_volatility * 0.2)  # Higher volatility = faster decay
        self.time_decay_factor = math.exp(-decay_rate * age_seconds / 30.0)  # 30-second half-life
        
        # Urgency based on time to expiry and volatility
        if time_to_expiry <= 0:
            self.urgency_level = UrgencyLevel.EXPIRED
        elif time_to_expiry <= 5 or self.market_volatility > 0.8:
            self.urgency_level = UrgencyLevel.CRITICAL
        elif time_to_expiry <= 10 or self.market_volatility > 0.6:
            self.urgency_level = UrgencyLevel.HIGH
        elif time_to_expiry <= 20 or self.market_volatility > 0.4:
            self.urgency_level = UrgencyLevel.MEDIUM
        else:
            self.urgency_level = UrgencyLevel.LOW
        
        # Time-weighted score (combines base score with time factors)
        urgency_multiplier = {
            UrgencyLevel.CRITICAL: 1.5,
            UrgencyLevel.HIGH: 1.2,
            UrgencyLevel.MEDIUM: 1.0,
            UrgencyLevel.LOW: 0.8,
            UrgencyLevel.EXPIRED: 0.0
        }[self.urgency_level]
        
        self.time_weighted_score = (
            self.base_profit_score * 
            self.time_decay_factor * 
            urgency_multiplier * 
            self.confidence
        )
        
        # Execution priority (higher = more urgent)
        complexity_penalty = max(0, 1.0 - self.execution_complexity)
        self.execution_priority = int(
            self.time_weighted_score * 1000 * complexity_penalty
        )
    
    def is_expired(self) -> bool:
        """Check if opportunity has expired"""
        return datetime.now() >= self.expires_at or self.urgency_level == UrgencyLevel.EXPIRED
    
    def should_execute_immediately(self) -> bool:
        """Check if opportunity requires immediate execution"""
        return self.urgency_level in [UrgencyLevel.CRITICAL, UrgencyLevel.HIGH]

class TimeAwareStrategyEngine:
    """Time-aware strategy engine with speed optimization"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.active_opportunities = {}
        self.execution_history = []
        self.performance_tracker = {
            'total_opportunities': 0,
            'executed_opportunities': 0,
            'expired_opportunities': 0,
            'avg_execution_time': 0.0,
            'success_rate': 0.0
        }
        
        # Speed optimization settings
        self.max_opportunity_age = config.get('max_opportunity_age', 30)  # seconds
        self.evaluation_batch_size = config.get('evaluation_batch_size', 10)
        self.parallel_evaluation = config.get('parallel_evaluation', True)
        
        # Start background cleanup
        self._cleanup_active = True
        asyncio.create_task(self._background_cleanup())
    
    @fast_strategy_evaluation
    async def evaluate_opportunity(self, market_data: Dict[str, Any], symbol: str) -> Optional[TimeAwareOpportunity]:
        """Evaluate trading opportunity with time-aware scoring"""
        try:
            # Quick pre-filtering for speed
            if not self._quick_viability_check(market_data, symbol):
                return None
            
            # Calculate base profit score
            base_score = await self._calculate_base_profit_score(market_data, symbol)
            if base_score < self.config.get('min_profit_score', 0.5):
                return None
            
            # Calculate confidence
            confidence = await self._calculate_confidence(market_data, symbol)
            if confidence < self.config.get('min_confidence', 0.6):
                return None
            
            # Determine action
            action = await self._determine_action(market_data, symbol)
            if not action:
                return None
            
            # Calculate market volatility and execution complexity
            volatility = self._calculate_market_volatility(market_data)
            complexity = self._calculate_execution_complexity(market_data, symbol)
            
            # Create time-aware opportunity
            current_time = datetime.now()
            expires_at = current_time + timedelta(seconds=self.max_opportunity_age)
            
            opportunity = TimeAwareOpportunity(
                symbol=symbol,
                action=action,
                base_profit_score=base_score,
                confidence=confidence,
                created_at=current_time,
                expires_at=expires_at,
                market_volatility=volatility,
                execution_complexity=complexity
            )
            
            # Store opportunity
            opportunity_id = f"{symbol}_{action}_{int(time.time() * 1000)}"
            self.active_opportunities[opportunity_id] = opportunity
            self.performance_tracker['total_opportunities'] += 1
            
            logger.info(f"🎯 [STRATEGY] New opportunity: {symbol} {action} "
                       f"(score: {opportunity.time_weighted_score:.3f}, "
                       f"urgency: {opportunity.urgency_level.value})")
            
            return opportunity
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY] Error evaluating opportunity for {symbol}: {e}")
            return None
    
    @fast_signal_generation
    async def generate_trading_signals(self, market_data_batch: List[Dict[str, Any]]) -> List[TimeAwareOpportunity]:
        """Generate trading signals with time-aware prioritization"""
        signals = []
        
        try:
            if self.parallel_evaluation and len(market_data_batch) > 1:
                # Parallel evaluation for speed
                tasks = [
                    self.evaluate_opportunity(data, data.get('symbol'))
                    for data in market_data_batch[:self.evaluation_batch_size]
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for result in results:
                    if isinstance(result, TimeAwareOpportunity):
                        signals.append(result)
                    elif isinstance(result, Exception):
                        logger.warning(f"⚠️ [STRATEGY] Evaluation error: {result}")
            else:
                # Sequential evaluation
                for data in market_data_batch[:self.evaluation_batch_size]:
                    opportunity = await self.evaluate_opportunity(data, data.get('symbol'))
                    if opportunity:
                        signals.append(opportunity)
            
            # Sort by execution priority (highest first)
            signals.sort(key=lambda x: x.execution_priority, reverse=True)
            
            # Update time factors for existing opportunities
            await self._update_existing_opportunities()
            
            # Filter out expired opportunities
            valid_signals = [s for s in signals if not s.is_expired()]
            
            logger.info(f"📊 [STRATEGY] Generated {len(valid_signals)} valid signals from {len(market_data_batch)} market data points")
            
            return valid_signals
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY] Error generating signals: {e}")
            return []
    
    async def get_execution_queue(self) -> List[TimeAwareOpportunity]:
        """Get prioritized execution queue"""
        # Update all opportunities
        await self._update_existing_opportunities()
        
        # Get non-expired opportunities
        valid_opportunities = [
            opp for opp in self.active_opportunities.values()
            if not opp.is_expired()
        ]
        
        # Sort by priority
        valid_opportunities.sort(key=lambda x: x.execution_priority, reverse=True)
        
        return valid_opportunities
    
    async def mark_opportunity_executed(self, opportunity_id: str, success: bool, execution_time: float):
        """Mark opportunity as executed"""
        if opportunity_id in self.active_opportunities:
            opportunity = self.active_opportunities.pop(opportunity_id)
            
            self.execution_history.append({
                'opportunity': opportunity,
                'success': success,
                'execution_time': execution_time,
                'executed_at': datetime.now()
            })
            
            # Update performance metrics
            self.performance_tracker['executed_opportunities'] += 1
            
            # Update average execution time
            total_time = (self.performance_tracker['avg_execution_time'] * 
                         (self.performance_tracker['executed_opportunities'] - 1) + 
                         execution_time)
            self.performance_tracker['avg_execution_time'] = (
                total_time / self.performance_tracker['executed_opportunities']
            )
            
            # Update success rate
            successful_executions = sum(1 for h in self.execution_history if h['success'])
            self.performance_tracker['success_rate'] = (
                successful_executions / len(self.execution_history) * 100
            )
            
            logger.info(f"✅ [STRATEGY] Opportunity executed: {opportunity.symbol} "
                       f"(success: {success}, time: {execution_time:.1f}ms)")
    
    def _quick_viability_check(self, market_data: Dict[str, Any], symbol: str) -> bool:
        """Quick viability check for speed optimization"""
        # Basic checks that can be done quickly
        if not symbol or not market_data:
            return False
        
        # Check if we have minimum required data
        required_fields = ['price', 'volume']
        if not all(field in market_data for field in required_fields):
            return False
        
        # Check if price and volume are reasonable
        price = market_data.get('price', 0)
        volume = market_data.get('volume', 0)
        
        if price <= 0 or volume <= 0:
            return False
        
        return True
    
    @cached_market_data(ttl_seconds=5.0)
    async def _calculate_base_profit_score(self, market_data: Dict[str, Any], symbol: str) -> float:
        """Calculate base profit score (cached for speed)"""
        # Simplified profit calculation for speed
        price = market_data.get('price', 0)
        volume = market_data.get('volume', 0)
        
        # Basic profit score based on price movement potential
        price_change = market_data.get('price_change_24h', 0)
        volume_score = min(1.0, volume / 1000000)  # Normalize volume
        
        base_score = abs(price_change) * volume_score * 0.01
        return min(1.0, base_score)
    
    async def _calculate_confidence(self, market_data: Dict[str, Any], symbol: str) -> float:
        """Calculate confidence score"""
        # Simplified confidence calculation
        volume = market_data.get('volume', 0)
        price_stability = 1.0 - abs(market_data.get('price_change_1h', 0)) / 100
        
        confidence = (volume / 1000000) * price_stability
        return min(1.0, max(0.0, confidence))
    
    async def _determine_action(self, market_data: Dict[str, Any], symbol: str) -> Optional[str]:
        """Determine trading action"""
        price_change = market_data.get('price_change_1h', 0)
        
        if price_change > 0.5:
            return 'buy'
        elif price_change < -0.5:
            return 'sell'
        
        return None
    
    def _calculate_market_volatility(self, market_data: Dict[str, Any]) -> float:
        """Calculate market volatility"""
        price_change_24h = abs(market_data.get('price_change_24h', 0))
        return min(1.0, price_change_24h / 10.0)  # Normalize to 0-1
    
    def _calculate_execution_complexity(self, market_data: Dict[str, Any], symbol: str) -> float:
        """Calculate execution complexity"""
        # Simple complexity based on volume and spread
        volume = market_data.get('volume', 0)
        spread = market_data.get('spread', 0.001)
        
        # Lower volume and higher spread = higher complexity
        volume_factor = max(0.1, min(1.0, volume / 1000000))
        spread_factor = min(1.0, spread * 1000)
        
        complexity = 1.0 - volume_factor + spread_factor
        return min(1.0, max(0.0, complexity))
    
    async def _update_existing_opportunities(self):
        """Update time factors for existing opportunities"""
        expired_ids = []
        
        for opp_id, opportunity in self.active_opportunities.items():
            opportunity.update_time_factors()
            
            if opportunity.is_expired():
                expired_ids.append(opp_id)
        
        # Remove expired opportunities
        for opp_id in expired_ids:
            self.active_opportunities.pop(opp_id, None)
            self.performance_tracker['expired_opportunities'] += 1
    
    async def _background_cleanup(self):
        """Background cleanup of expired opportunities"""
        while self._cleanup_active:
            try:
                await self._update_existing_opportunities()
                await asyncio.sleep(5)  # Cleanup every 5 seconds
            except Exception as e:
                logger.error(f"Error in background cleanup: {e}")
                await asyncio.sleep(10)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get strategy performance metrics"""
        return {
            **self.performance_tracker,
            'active_opportunities': len(self.active_opportunities),
            'recent_execution_history': self.execution_history[-10:],
            'speed_metrics': speed_optimizer.get_performance_report()
        }
    
    def shutdown(self):
        """Shutdown strategy engine"""
        self._cleanup_active = False
