"""
Risk limits and constraints for trading operations
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from decimal import Decimal
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class PositionLimits:
    """Position size limits for risk management"""
    max_position_size: Decimal
    max_position_value: Decimal
    max_daily_trades: int
    max_concentration: float  # Maximum % of portfolio in single asset

@dataclass
class ExposureLimits:
    """Exposure limits across different dimensions"""
    max_total_exposure: Decimal
    max_currency_exposure: Dict[str, Decimal]
    max_exchange_exposure: Dict[str, Decimal]
    max_sector_exposure: Dict[str, float]

@dataclass
class RiskLimits:
    """Comprehensive risk limits for trading system"""
    max_var: float  # Maximum Value at Risk
    max_cvar: float  # Maximum Conditional VaR
    max_drawdown: float  # Maximum allowed drawdown
    min_sharpe_ratio: float  # Minimum Sharpe ratio
    max_volatility: float  # Maximum volatility
    
    position_limits: PositionLimits
    exposure_limits: ExposureLimits
    
    def __post_init__(self):
        """Validate limits after initialization"""
        if self.max_var <= 0:
            self.max_var = 0.05  # 5% default
        if self.max_cvar <= 0:
            self.max_cvar = 0.08  # 8% default
        if self.max_drawdown <= 0:
            self.max_drawdown = 0.15  # 15% default

class RiskLimitChecker:
    """Check trading operations against risk limits"""
    
    def __init__(self, limits: RiskLimits):
        self.limits = limits
        self.current_positions = {}
        self.daily_trades = 0
        self.last_reset_date = datetime.now().date()
    
    def check_position_limit(self, symbol: str, new_position_size: Decimal, 
                           current_price: float) -> bool:
        """Check if new position would exceed limits"""
        try:
            # Reset daily counter if new day
            current_date = datetime.now().date()
            if current_date != self.last_reset_date:
                self.daily_trades = 0
                self.last_reset_date = current_date
            
            # Check daily trade limit
            if self.daily_trades >= self.limits.position_limits.max_daily_trades:
                logger.warning(f"Daily trade limit exceeded: {self.daily_trades}")
                return False
            
            # Check position size limit
            if abs(new_position_size) > self.limits.position_limits.max_position_size:
                logger.warning(f"Position size limit exceeded: {new_position_size}")
                return False
            
            # Check position value limit
            position_value = abs(new_position_size) * Decimal(str(current_price))
            if position_value > self.limits.position_limits.max_position_value:
                logger.warning(f"Position value limit exceeded: {position_value}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking position limit: {e}")
            return False
    
    def check_exposure_limit(self, currency: str, additional_exposure: Decimal) -> bool:
        """Check if additional exposure would exceed currency limits"""
        try:
            current_exposure = self.limits.exposure_limits.max_currency_exposure.get(
                currency, Decimal('0')
            )
            
            max_allowed = self.limits.exposure_limits.max_currency_exposure.get(
                currency, Decimal('1000000')  # Default large limit
            )
            
            if current_exposure + additional_exposure > max_allowed:
                logger.warning(f"Currency exposure limit exceeded for {currency}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking exposure limit: {e}")
            return False
    
    def check_risk_metrics(self, var: float, cvar: float, drawdown: float, 
                          volatility: float) -> bool:
        """Check if risk metrics are within acceptable limits"""
        try:
            if var > self.limits.max_var:
                logger.warning(f"VaR limit exceeded: {var} > {self.limits.max_var}")
                return False
            
            if cvar > self.limits.max_cvar:
                logger.warning(f"CVaR limit exceeded: {cvar} > {self.limits.max_cvar}")
                return False
            
            if drawdown > self.limits.max_drawdown:
                logger.warning(f"Drawdown limit exceeded: {drawdown} > {self.limits.max_drawdown}")
                return False
            
            if volatility > self.limits.max_volatility:
                logger.warning(f"Volatility limit exceeded: {volatility} > {self.limits.max_volatility}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking risk metrics: {e}")
            return False
    
    def update_position(self, symbol: str, position_size: Decimal):
        """Update current position tracking"""
        self.current_positions[symbol] = position_size
        self.daily_trades += 1
    
    def get_current_exposure(self) -> Dict[str, Decimal]:
        """Get current exposure by currency"""
        exposure = {}
        for symbol, position in self.current_positions.items():
            # Extract currency from symbol (simplified)
            currency = symbol.replace('USDT', '').replace('USD', '')
            if currency not in exposure:
                exposure[currency] = Decimal('0')
            exposure[currency] += abs(position)
        
        return exposure
