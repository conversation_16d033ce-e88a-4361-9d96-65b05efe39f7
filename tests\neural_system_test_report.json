{"test_summary": {"timestamp": "2025-06-28T15:54:56.316310+00:00", "total_tests": 9, "passed_tests": 8, "failed_tests": 1, "skipped_tests": 0}, "detailed_results": {"component_initialization": {"status": "completed", "details": {"profit_predictor": "success", "feature_engineer": "success", "sentiment_scorer": "success", "regime_detector": "success", "lstm_processor": "success", "rl_agent": "success"}, "success_rate": 1.0, "successful_components": 6, "total_components": 6}, "feature_engineering": {"status": "completed", "details": {"fear_greed_features": {"status": "success", "feature_count": 17, "sample_features": ["fg_current", "fg_normalized", "fg_trend_3h", "fg_trend_12h", "fg_trend_24h"]}, "etherscan_features": {"status": "success", "feature_count": 13, "sample_features": ["eth_gas_price_norm", "eth_network_util", "eth_active_addresses_norm", "eth_tx_count_norm", "eth_whale_activity"]}, "glassnode_features": {"status": "success", "feature_count": 13, "sample_features": ["btc_hash_rate_norm", "btc_mempool_norm", "btc_active_addresses_norm", "btc_hodl_waves", "defi_tvl_norm"]}}, "success_rate": 1.0}, "sentiment_scoring": {"status": "completed", "details": {"composite_sentiment": {"status": "success", "composite_score": 0.3285909090909091, "confidence": 0.8373182534358931, "component_count": 6, "signal_count": 7}, "sentiment_summary": {"status": "success", "summary_keys": ["current_sentiment", "current_confidence", "avg_sentiment_24h", "sentiment_volatility_24h", "avg_confidence_24h", "sentiment_trend", "data_points", "last_update"]}}}, "regime_detection": {"status": "completed", "details": {"regime_detection": {"status": "success", "primary_regime": "bull_market", "confidence": 0.5, "probability_count": 9, "factor_count": 12, "trading_implications": ["strategy", "position_bias", "risk_level", "entry_signals", "exit_signals", "confidence_level", "position_size", "stop_loss_distance", "profit_target_ratio", "max_drawdown_tolerance"]}}}, "neural_predictions": {"status": "completed", "details": {"profit_prediction": {"status": "failed", "error": "'ProfitPrediction' object has no attribute 'profit_score'"}, "lstm_prediction": {"status": "success", "prediction": 0.018144, "confidence": 0.8384, "features_used": 30}}}, "reinforcement_learning": {"status": "completed", "details": {"action_prediction": {"status": "success", "best_action": 2, "confidence": "0.22798868", "action_space_size": 5}, "enhanced_reward": {"status": "success", "reward_value": 0.148295}}}, "graph_neural_network": {"status": "completed", "details": {"gnn_functionality": {"status": "success", "total_nodes": 21, "sentiment_nodes_shape": [5, 32], "onchain_nodes_shape": [8, 48]}}}, "system_integration": {"status": "completed", "details": {"pipeline_results": {"feature_extraction": {"status": "success", "total_features": 43}, "sentiment_analysis": {"status": "success", "composite_score": 0.3285909090909091, "confidence": 0.8373182534358931}, "regime_detection": {"status": "success", "regime": "bull_market", "confidence": 0.5}}, "integration_success": true}}, "performance_metrics": {"status": "failed", "details": {}, "error": "cannot access local variable 'statistics' where it is not associated with a value"}}}