"""
Enhanced Market Regime Detection System
Advanced regime classification using sentiment, on-chain data, and market factors
"""

import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum
import asyncio

logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """Market regime classifications"""
    BULL_MARKET = "bull_market"
    BEAR_MARKET = "bear_market"
    SIDEWAYS_MARKET = "sideways_market"
    HIGH_VOLATILITY = "high_volatility"
    ACCUMULATION = "accumulation"
    DISTRIBUTION = "distribution"
    CAPITULATION = "capitulation"
    EUPHORIA = "euphoria"
    UNCERTAINTY = "uncertainty"

@dataclass
class RegimeDetection:
    """Market regime detection result"""
    primary_regime: MarketRegime
    regime_confidence: float  # 0 to 1
    regime_probabilities: Dict[MarketRegime, float]
    regime_factors: Dict[str, float]
    trading_implications: Dict[str, Any]
    timestamp: datetime

class MarketRegimeDetector:
    """
    Advanced market regime detection using multiple data sources
    Combines sentiment, on-chain data, technical analysis, and market structure
    """
    
    def __init__(self):
        # Regime detection weights
        self.regime_weights = {
            'sentiment_factors': 0.25,
            'onchain_factors': 0.25,
            'technical_factors': 0.20,
            'market_structure': 0.15,
            'volatility_factors': 0.15
        }
        
        # Historical regime data for trend analysis
        self.regime_history = []
        self.max_history = 168  # 1 week of hourly data
        
        # Regime transition thresholds
        self.transition_thresholds = {
            'high_confidence': 0.8,
            'medium_confidence': 0.6,
            'low_confidence': 0.4
        }
        
        logger.info("Market Regime Detector initialized")
    
    async def detect_market_regime(self, market_data: Dict[str, Any], 
                                 sentiment_data: Optional[Dict[str, Any]] = None,
                                 composite_sentiment: Optional[float] = None) -> RegimeDetection:
        """
        Detect current market regime using comprehensive analysis
        
        Args:
            market_data: Market data including prices, volumes, on-chain data
            sentiment_data: Sentiment analysis data
            composite_sentiment: Pre-calculated composite sentiment score
            
        Returns:
            RegimeDetection object with regime classification and confidence
        """
        try:
            # Calculate regime factors
            regime_factors = {}
            
            # 1. Sentiment-based regime factors
            sentiment_factors = await self._calculate_sentiment_regime_factors(
                market_data, sentiment_data, composite_sentiment
            )
            regime_factors.update(sentiment_factors)
            
            # 2. On-chain regime factors
            onchain_factors = await self._calculate_onchain_regime_factors(market_data)
            regime_factors.update(onchain_factors)
            
            # 3. Technical regime factors
            technical_factors = await self._calculate_technical_regime_factors(market_data)
            regime_factors.update(technical_factors)
            
            # 4. Market structure factors
            structure_factors = await self._calculate_market_structure_factors(market_data)
            regime_factors.update(structure_factors)
            
            # 5. Volatility regime factors
            volatility_factors = await self._calculate_volatility_regime_factors(market_data)
            regime_factors.update(volatility_factors)
            
            # Calculate regime probabilities
            regime_probabilities = await self._calculate_regime_probabilities(regime_factors)
            
            # Determine primary regime
            primary_regime = max(regime_probabilities, key=regime_probabilities.get)
            regime_confidence = regime_probabilities[primary_regime]
            
            # Generate trading implications
            trading_implications = await self._generate_trading_implications(
                primary_regime, regime_confidence, regime_factors
            )
            
            # Create regime detection result
            detection = RegimeDetection(
                primary_regime=primary_regime,
                regime_confidence=regime_confidence,
                regime_probabilities=regime_probabilities,
                regime_factors=regime_factors,
                trading_implications=trading_implications,
                timestamp=datetime.now(timezone.utc)
            )
            
            # Update history
            self._update_regime_history(detection)
            
            logger.debug(f"Market regime detected: {primary_regime.value} "
                        f"(confidence: {regime_confidence:.3f})")
            
            return detection
            
        except Exception as e:
            logger.error(f"Error detecting market regime: {e}")
            return self._get_default_regime_detection()
    
    async def _calculate_sentiment_regime_factors(self, market_data: Dict[str, Any],
                                                sentiment_data: Optional[Dict[str, Any]],
                                                composite_sentiment: Optional[float]) -> Dict[str, float]:
        """Calculate regime factors from sentiment data"""
        try:
            factors = {}
            
            # Fear & Greed Index regime indicators
            live_aggregated = market_data.get('live_aggregated', {})
            fear_greed_index = live_aggregated.get('fear_greed_index', 65.0)
            
            # Extreme fear suggests capitulation or accumulation
            if fear_greed_index <= 25:
                factors['extreme_fear_signal'] = 1.0
                factors['capitulation_signal'] = 0.8
                factors['accumulation_signal'] = 0.6
            else:
                factors['extreme_fear_signal'] = 0.0
                factors['capitulation_signal'] = 0.0
                factors['accumulation_signal'] = 0.0
            
            # Extreme greed suggests euphoria or distribution
            if fear_greed_index >= 75:
                factors['extreme_greed_signal'] = 1.0
                factors['euphoria_signal'] = 0.8
                factors['distribution_signal'] = 0.6
            else:
                factors['extreme_greed_signal'] = 0.0
                factors['euphoria_signal'] = 0.0
                factors['distribution_signal'] = 0.0
            
            # Composite sentiment regime factors
            if composite_sentiment is not None:
                if composite_sentiment >= 0.6:
                    factors['bullish_sentiment'] = 1.0
                elif composite_sentiment <= -0.6:
                    factors['bearish_sentiment'] = 1.0
                else:
                    factors['neutral_sentiment'] = 1.0
                    factors['bullish_sentiment'] = 0.0
                    factors['bearish_sentiment'] = 0.0
            else:
                factors['neutral_sentiment'] = 1.0
                factors['bullish_sentiment'] = 0.0
                factors['bearish_sentiment'] = 0.0
            
            # Web sentiment regime factors
            web_insights = market_data.get('web_crawler_insights', {})
            if web_insights:
                high_impact_count = web_insights.get('high_impact_count', 0)
                if high_impact_count >= 5:
                    factors['high_news_impact'] = 1.0
                    factors['uncertainty_signal'] = 0.7
                else:
                    factors['high_news_impact'] = 0.0
                    factors['uncertainty_signal'] = 0.0
            
            return factors
            
        except Exception as e:
            logger.error(f"Error calculating sentiment regime factors: {e}")
            return {}
    
    async def _calculate_onchain_regime_factors(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate regime factors from on-chain data"""
        try:
            factors = {}
            live_aggregated = market_data.get('live_aggregated', {})
            
            # Bitcoin on-chain regime indicators
            btc_data = live_aggregated.get('bitcoin_data', {})
            if btc_data:
                # Hash rate growth indicates network strength
                hash_rate_norm = btc_data.get('hash_rate', 300e18) / 400e18
                if hash_rate_norm > 0.9:
                    factors['strong_network_growth'] = 1.0
                elif hash_rate_norm < 0.6:
                    factors['weak_network_signal'] = 1.0
                
                # HODL waves indicate long-term confidence
                hodl_waves = btc_data.get('hodl_waves', 0.6)
                if hodl_waves > 0.7:
                    factors['strong_hodl_behavior'] = 1.0
                    factors['accumulation_onchain'] = 0.8
                elif hodl_waves < 0.4:
                    factors['weak_hodl_behavior'] = 1.0
                    factors['distribution_onchain'] = 0.8
                
                # Exchange flows
                exchange_flows = btc_data.get('exchange_flows', 0.0)
                if exchange_flows < -0.5:  # Outflows from exchanges
                    factors['accumulation_flows'] = 1.0
                elif exchange_flows > 0.5:  # Inflows to exchanges
                    factors['distribution_flows'] = 1.0
            
            # Ethereum DeFi regime indicators
            defi_data = live_aggregated.get('defi_data', {})
            if defi_data:
                # TVL growth indicates DeFi adoption
                tvl_norm = defi_data.get('total_value_locked', 50e9) / 100e9
                if tvl_norm > 0.8:
                    factors['defi_growth_regime'] = 1.0
                elif tvl_norm < 0.3:
                    factors['defi_contraction_regime'] = 1.0
                
                # Liquidation risk
                liquidation_risk = defi_data.get('liquidation_risk', 0.1)
                if liquidation_risk > 0.3:
                    factors['high_liquidation_risk'] = 1.0
                    factors['stress_regime'] = 0.8
            
            # Institutional adoption indicators
            institutional_data = live_aggregated.get('institutional_data', {})
            if institutional_data:
                etf_flows = institutional_data.get('etf_flows', 0.0)
                if abs(etf_flows) > 1e9:  # Large institutional flows
                    factors['institutional_activity'] = 1.0
                    if etf_flows > 0:
                        factors['institutional_buying'] = 1.0
                    else:
                        factors['institutional_selling'] = 1.0
            
            return factors
            
        except Exception as e:
            logger.error(f"Error calculating on-chain regime factors: {e}")
            return {}
    
    async def _calculate_technical_regime_factors(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate regime factors from technical analysis"""
        try:
            factors = {}
            
            # Price action regime factors
            price_data = market_data.get('price_data', {})
            if price_data:
                price_changes_24h = []
                for symbol_data in price_data.values():
                    if 'price_change_24h' in symbol_data:
                        price_changes_24h.append(float(symbol_data['price_change_24h']))
                
                if price_changes_24h:
                    avg_change = np.mean(price_changes_24h)
                    positive_ratio = sum(1 for change in price_changes_24h if change > 0) / len(price_changes_24h)
                    
                    # Bull market indicators
                    if avg_change > 5 and positive_ratio > 0.7:
                        factors['strong_bull_signal'] = 1.0
                    elif avg_change > 2 and positive_ratio > 0.6:
                        factors['moderate_bull_signal'] = 1.0
                    
                    # Bear market indicators
                    elif avg_change < -5 and positive_ratio < 0.3:
                        factors['strong_bear_signal'] = 1.0
                    elif avg_change < -2 and positive_ratio < 0.4:
                        factors['moderate_bear_signal'] = 1.0
                    
                    # Sideways market
                    elif abs(avg_change) < 1 and 0.4 <= positive_ratio <= 0.6:
                        factors['sideways_signal'] = 1.0
            
            # Technical indicators regime factors
            technical_data = market_data.get('technical_indicators', {})
            if technical_data:
                rsi = technical_data.get('rsi', 50.0)
                
                # Overbought/oversold regimes
                if rsi > 80:
                    factors['extreme_overbought'] = 1.0
                    factors['distribution_technical'] = 0.8
                elif rsi < 20:
                    factors['extreme_oversold'] = 1.0
                    factors['accumulation_technical'] = 0.8
                elif 70 < rsi <= 80:
                    factors['overbought'] = 1.0
                elif 20 <= rsi < 30:
                    factors['oversold'] = 1.0
            
            return factors
            
        except Exception as e:
            logger.error(f"Error calculating technical regime factors: {e}")
            return {}
    
    async def _calculate_market_structure_factors(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate regime factors from market structure"""
        try:
            factors = {}
            
            # Volume analysis
            price_data = market_data.get('price_data', {})
            if price_data:
                volume_changes = []
                for symbol_data in price_data.values():
                    if 'volume_change_24h' in symbol_data:
                        volume_changes.append(float(symbol_data['volume_change_24h']))
                
                if volume_changes:
                    avg_volume_change = np.mean(volume_changes)
                    
                    # High volume regimes
                    if avg_volume_change > 50:
                        factors['high_volume_regime'] = 1.0
                        factors['breakout_potential'] = 0.8
                    elif avg_volume_change < -30:
                        factors['low_volume_regime'] = 1.0
                        factors['consolidation_signal'] = 0.8
            
            # Market breadth (if multiple symbols available)
            if len(price_data) >= 5:
                advancing_count = 0
                declining_count = 0
                
                for symbol_data in price_data.values():
                    change_24h = symbol_data.get('price_change_24h', 0)
                    if change_24h > 0:
                        advancing_count += 1
                    elif change_24h < 0:
                        declining_count += 1
                
                total_symbols = advancing_count + declining_count
                if total_symbols > 0:
                    advance_decline_ratio = advancing_count / total_symbols
                    
                    if advance_decline_ratio > 0.8:
                        factors['broad_market_strength'] = 1.0
                    elif advance_decline_ratio < 0.2:
                        factors['broad_market_weakness'] = 1.0
                    else:
                        factors['mixed_market_signals'] = 1.0
            
            return factors
            
        except Exception as e:
            logger.error(f"Error calculating market structure factors: {e}")
            return {}
    
    async def _calculate_volatility_regime_factors(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate regime factors from volatility analysis"""
        try:
            factors = {}
            
            price_data = market_data.get('price_data', {})
            if price_data:
                volatilities = []
                for symbol_data in price_data.values():
                    if 'volatility' in symbol_data:
                        volatilities.append(float(symbol_data['volatility']))
                
                if volatilities:
                    avg_volatility = np.mean(volatilities)
                    volatility_std = np.std(volatilities)
                    
                    # High volatility regime
                    if avg_volatility > 0.15:
                        factors['high_volatility_regime'] = 1.0
                        factors['stress_environment'] = 0.8
                    
                    # Low volatility regime
                    elif avg_volatility < 0.05:
                        factors['low_volatility_regime'] = 1.0
                        factors['calm_environment'] = 0.8
                    
                    # Volatility clustering
                    if volatility_std > 0.05:
                        factors['volatility_clustering'] = 1.0
                        factors['regime_transition'] = 0.7
            
            return factors
            
        except Exception as e:
            logger.error(f"Error calculating volatility regime factors: {e}")
            return {}

    async def _calculate_regime_probabilities(self, regime_factors: Dict[str, float]) -> Dict[MarketRegime, float]:
        """Calculate probabilities for each market regime"""
        try:
            regime_scores = {regime: 0.0 for regime in MarketRegime}

            # Bull market scoring
            bull_factors = [
                'bullish_sentiment', 'strong_bull_signal', 'moderate_bull_signal',
                'strong_network_growth', 'strong_hodl_behavior', 'accumulation_flows',
                'defi_growth_regime', 'institutional_buying', 'broad_market_strength'
            ]
            regime_scores[MarketRegime.BULL_MARKET] = sum(regime_factors.get(factor, 0.0) for factor in bull_factors)

            # Bear market scoring
            bear_factors = [
                'bearish_sentiment', 'strong_bear_signal', 'moderate_bear_signal',
                'weak_network_signal', 'weak_hodl_behavior', 'distribution_flows',
                'defi_contraction_regime', 'institutional_selling', 'broad_market_weakness'
            ]
            regime_scores[MarketRegime.BEAR_MARKET] = sum(regime_factors.get(factor, 0.0) for factor in bear_factors)

            # Sideways market scoring
            sideways_factors = [
                'neutral_sentiment', 'sideways_signal', 'mixed_market_signals',
                'low_volume_regime', 'consolidation_signal', 'calm_environment'
            ]
            regime_scores[MarketRegime.SIDEWAYS_MARKET] = sum(regime_factors.get(factor, 0.0) for factor in sideways_factors)

            # High volatility scoring
            volatility_factors = [
                'high_volatility_regime', 'stress_environment', 'volatility_clustering',
                'high_news_impact', 'high_liquidation_risk', 'stress_regime'
            ]
            regime_scores[MarketRegime.HIGH_VOLATILITY] = sum(regime_factors.get(factor, 0.0) for factor in volatility_factors)

            # Accumulation scoring
            accumulation_factors = [
                'extreme_fear_signal', 'accumulation_signal', 'accumulation_onchain',
                'accumulation_technical', 'extreme_oversold', 'capitulation_signal'
            ]
            regime_scores[MarketRegime.ACCUMULATION] = sum(regime_factors.get(factor, 0.0) for factor in accumulation_factors)

            # Distribution scoring
            distribution_factors = [
                'extreme_greed_signal', 'distribution_signal', 'distribution_onchain',
                'distribution_technical', 'extreme_overbought', 'euphoria_signal'
            ]
            regime_scores[MarketRegime.DISTRIBUTION] = sum(regime_factors.get(factor, 0.0) for factor in distribution_factors)

            # Capitulation scoring
            capitulation_factors = [
                'capitulation_signal', 'extreme_fear_signal', 'extreme_oversold',
                'high_liquidation_risk', 'broad_market_weakness'
            ]
            regime_scores[MarketRegime.CAPITULATION] = sum(regime_factors.get(factor, 0.0) for factor in capitulation_factors)

            # Euphoria scoring
            euphoria_factors = [
                'euphoria_signal', 'extreme_greed_signal', 'extreme_overbought',
                'broad_market_strength', 'high_volume_regime'
            ]
            regime_scores[MarketRegime.EUPHORIA] = sum(regime_factors.get(factor, 0.0) for factor in euphoria_factors)

            # Uncertainty scoring
            uncertainty_factors = [
                'uncertainty_signal', 'high_news_impact', 'regime_transition',
                'volatility_clustering', 'mixed_market_signals'
            ]
            regime_scores[MarketRegime.UNCERTAINTY] = sum(regime_factors.get(factor, 0.0) for factor in uncertainty_factors)

            # Normalize scores to probabilities
            total_score = sum(regime_scores.values())
            if total_score > 0:
                regime_probabilities = {regime: score / total_score for regime, score in regime_scores.items()}
            else:
                # Default equal probabilities
                regime_probabilities = {regime: 1.0 / len(MarketRegime) for regime in MarketRegime}

            return regime_probabilities

        except Exception as e:
            logger.error(f"Error calculating regime probabilities: {e}")
            return {regime: 1.0 / len(MarketRegime) for regime in MarketRegime}

    async def _generate_trading_implications(self, primary_regime: MarketRegime,
                                           confidence: float,
                                           regime_factors: Dict[str, float]) -> Dict[str, Any]:
        """Generate trading implications based on detected regime"""
        try:
            implications = {}

            # Regime-specific trading strategies
            if primary_regime == MarketRegime.BULL_MARKET:
                implications['strategy'] = 'trend_following'
                implications['position_bias'] = 'long'
                implications['risk_level'] = 'moderate'
                implications['entry_signals'] = ['breakouts', 'pullback_buying']
                implications['exit_signals'] = ['momentum_divergence', 'volume_decline']

            elif primary_regime == MarketRegime.BEAR_MARKET:
                implications['strategy'] = 'defensive'
                implications['position_bias'] = 'short'
                implications['risk_level'] = 'high'
                implications['entry_signals'] = ['breakdown_confirmation', 'bear_flag_patterns']
                implications['exit_signals'] = ['oversold_bounce', 'volume_exhaustion']

            elif primary_regime == MarketRegime.SIDEWAYS_MARKET:
                implications['strategy'] = 'range_trading'
                implications['position_bias'] = 'neutral'
                implications['risk_level'] = 'low'
                implications['entry_signals'] = ['support_bounce', 'resistance_rejection']
                implications['exit_signals'] = ['range_breakout', 'volume_expansion']

            elif primary_regime == MarketRegime.HIGH_VOLATILITY:
                implications['strategy'] = 'volatility_trading'
                implications['position_bias'] = 'neutral'
                implications['risk_level'] = 'very_high'
                implications['entry_signals'] = ['volatility_breakout', 'mean_reversion']
                implications['exit_signals'] = ['volatility_contraction', 'time_stop']

            elif primary_regime == MarketRegime.ACCUMULATION:
                implications['strategy'] = 'accumulation'
                implications['position_bias'] = 'long'
                implications['risk_level'] = 'moderate'
                implications['entry_signals'] = ['fear_extremes', 'value_opportunities']
                implications['exit_signals'] = ['sentiment_improvement', 'technical_breakout']

            elif primary_regime == MarketRegime.DISTRIBUTION:
                implications['strategy'] = 'distribution'
                implications['position_bias'] = 'short'
                implications['risk_level'] = 'moderate'
                implications['entry_signals'] = ['greed_extremes', 'technical_weakness']
                implications['exit_signals'] = ['sentiment_deterioration', 'support_break']

            elif primary_regime == MarketRegime.CAPITULATION:
                implications['strategy'] = 'contrarian'
                implications['position_bias'] = 'long'
                implications['risk_level'] = 'high'
                implications['entry_signals'] = ['panic_selling', 'volume_climax']
                implications['exit_signals'] = ['sentiment_stabilization', 'technical_recovery']

            elif primary_regime == MarketRegime.EUPHORIA:
                implications['strategy'] = 'contrarian'
                implications['position_bias'] = 'short'
                implications['risk_level'] = 'high'
                implications['entry_signals'] = ['euphoric_buying', 'technical_divergence']
                implications['exit_signals'] = ['sentiment_cooling', 'volume_decline']

            elif primary_regime == MarketRegime.UNCERTAINTY:
                implications['strategy'] = 'wait_and_see'
                implications['position_bias'] = 'neutral'
                implications['risk_level'] = 'very_high'
                implications['entry_signals'] = ['clear_direction', 'volatility_breakout']
                implications['exit_signals'] = ['increased_uncertainty', 'stop_loss']

            # Confidence-based adjustments
            if confidence >= self.transition_thresholds['high_confidence']:
                implications['confidence_level'] = 'high'
                implications['position_size'] = 'normal'
            elif confidence >= self.transition_thresholds['medium_confidence']:
                implications['confidence_level'] = 'medium'
                implications['position_size'] = 'reduced'
            else:
                implications['confidence_level'] = 'low'
                implications['position_size'] = 'minimal'

            # Risk management recommendations
            implications['stop_loss_distance'] = self._calculate_stop_loss_distance(primary_regime, confidence)
            implications['profit_target_ratio'] = self._calculate_profit_target_ratio(primary_regime, confidence)
            implications['max_drawdown_tolerance'] = self._calculate_max_drawdown(primary_regime, confidence)

            return implications

        except Exception as e:
            logger.error(f"Error generating trading implications: {e}")
            return {'strategy': 'wait_and_see', 'error': str(e)}

    def _calculate_stop_loss_distance(self, regime: MarketRegime, confidence: float) -> float:
        """Calculate recommended stop loss distance based on regime"""
        base_distances = {
            MarketRegime.BULL_MARKET: 0.05,
            MarketRegime.BEAR_MARKET: 0.03,
            MarketRegime.SIDEWAYS_MARKET: 0.02,
            MarketRegime.HIGH_VOLATILITY: 0.08,
            MarketRegime.ACCUMULATION: 0.06,
            MarketRegime.DISTRIBUTION: 0.04,
            MarketRegime.CAPITULATION: 0.10,
            MarketRegime.EUPHORIA: 0.07,
            MarketRegime.UNCERTAINTY: 0.04
        }

        base_distance = base_distances.get(regime, 0.05)

        # Adjust based on confidence
        confidence_multiplier = 1.0 + (1.0 - confidence) * 0.5

        return base_distance * confidence_multiplier

    def _calculate_profit_target_ratio(self, regime: MarketRegime, confidence: float) -> float:
        """Calculate recommended profit target ratio based on regime"""
        base_ratios = {
            MarketRegime.BULL_MARKET: 3.0,
            MarketRegime.BEAR_MARKET: 2.0,
            MarketRegime.SIDEWAYS_MARKET: 1.5,
            MarketRegime.HIGH_VOLATILITY: 2.5,
            MarketRegime.ACCUMULATION: 4.0,
            MarketRegime.DISTRIBUTION: 2.5,
            MarketRegime.CAPITULATION: 5.0,
            MarketRegime.EUPHORIA: 1.5,
            MarketRegime.UNCERTAINTY: 1.0
        }

        base_ratio = base_ratios.get(regime, 2.0)

        # Adjust based on confidence
        confidence_multiplier = 0.5 + confidence * 0.5

        return base_ratio * confidence_multiplier

    def _calculate_max_drawdown(self, regime: MarketRegime, confidence: float) -> float:
        """Calculate maximum acceptable drawdown based on regime"""
        base_drawdowns = {
            MarketRegime.BULL_MARKET: 0.15,
            MarketRegime.BEAR_MARKET: 0.10,
            MarketRegime.SIDEWAYS_MARKET: 0.08,
            MarketRegime.HIGH_VOLATILITY: 0.20,
            MarketRegime.ACCUMULATION: 0.12,
            MarketRegime.DISTRIBUTION: 0.10,
            MarketRegime.CAPITULATION: 0.25,
            MarketRegime.EUPHORIA: 0.15,
            MarketRegime.UNCERTAINTY: 0.05
        }

        return base_drawdowns.get(regime, 0.10)

    def _update_regime_history(self, detection: RegimeDetection):
        """Update regime history for trend analysis"""
        try:
            self.regime_history.append(detection)

            # Keep only recent history
            if len(self.regime_history) > self.max_history:
                self.regime_history = self.regime_history[-self.max_history:]

        except Exception as e:
            logger.error(f"Error updating regime history: {e}")

    def _get_default_regime_detection(self) -> RegimeDetection:
        """Get default regime detection for error cases"""
        return RegimeDetection(
            primary_regime=MarketRegime.UNCERTAINTY,
            regime_confidence=0.5,
            regime_probabilities={regime: 1.0 / len(MarketRegime) for regime in MarketRegime},
            regime_factors={},
            trading_implications={'strategy': 'wait_and_see', 'error': 'Default values used'},
            timestamp=datetime.now(timezone.utc)
        )

    def get_regime_summary(self) -> Dict[str, Any]:
        """Get summary of recent regime analysis"""
        try:
            if not self.regime_history:
                return {'status': 'No regime history available'}

            recent_regimes = [entry.primary_regime.value for entry in self.regime_history[-24:]]
            recent_confidences = [entry.regime_confidence for entry in self.regime_history[-24:]]

            # Calculate regime stability
            regime_changes = sum(1 for i in range(1, len(recent_regimes))
                               if recent_regimes[i] != recent_regimes[i-1])

            return {
                'current_regime': self.regime_history[-1].primary_regime.value,
                'current_confidence': self.regime_history[-1].regime_confidence,
                'avg_confidence_24h': np.mean(recent_confidences),
                'regime_stability': 1.0 - (regime_changes / max(1, len(recent_regimes) - 1)),
                'regime_changes_24h': regime_changes,
                'data_points': len(self.regime_history),
                'last_update': self.regime_history[-1].timestamp.isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting regime summary: {e}")
            return {'error': str(e)}

# Export the main classes
__all__ = ['MarketRegimeDetector', 'RegimeDetection', 'MarketRegime']
