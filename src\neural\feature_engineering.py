"""
Enhanced Feature Engineering for Neural Trading System
Specialized feature extraction for Fear & Greed Index, Etherscan, and Glassnode data
"""

import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone, timedelta
from collections import deque
import asyncio

logger = logging.getLogger(__name__)

class FeatureEngineer:
    """
    Advanced feature engineering for trading neural networks
    Specializes in sentiment, on-chain, and market regime features
    """
    
    def __init__(self, history_length: int = 168):  # 1 week of hourly data
        self.history_length = history_length
        
        # Historical data storage for trend analysis
        self.fear_greed_history = deque(maxlen=history_length)
        self.etherscan_history = deque(maxlen=history_length)
        self.glassnode_history = deque(maxlen=history_length)
        self.price_history = deque(maxlen=history_length)
        
        logger.info(f"Feature Engineer initialized with {history_length} hour history")
    
    async def extract_fear_greed_features(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract comprehensive Fear & Greed Index trend patterns"""
        try:
            live_aggregated = market_data.get('live_aggregated', {})
            current_fg = live_aggregated.get('fear_greed_index', 65.0)
            
            # Store current value
            self.fear_greed_history.append({
                'timestamp': datetime.now(timezone.utc),
                'value': current_fg
            })
            
            features = {}
            
            # Basic Fear & Greed features
            features['fg_current'] = current_fg / 100.0  # Normalize to 0-1
            features['fg_normalized'] = (current_fg - 50) / 50.0  # Center around 0
            
            # Trend analysis features
            if len(self.fear_greed_history) >= 3:
                values = [entry['value'] for entry in self.fear_greed_history]
                
                # Short-term trends (last 3 hours)
                features['fg_trend_3h'] = self._calculate_trend(values[-3:])
                
                # Medium-term trends (last 12 hours)
                if len(values) >= 12:
                    features['fg_trend_12h'] = self._calculate_trend(values[-12:])
                else:
                    features['fg_trend_12h'] = 0.0
                
                # Long-term trends (last 24 hours)
                if len(values) >= 24:
                    features['fg_trend_24h'] = self._calculate_trend(values[-24:])
                else:
                    features['fg_trend_24h'] = 0.0
                
                # Volatility features
                features['fg_volatility_3h'] = np.std(values[-3:]) / 100.0
                features['fg_volatility_12h'] = np.std(values[-12:]) / 100.0 if len(values) >= 12 else 0.0
                features['fg_volatility_24h'] = np.std(values[-24:]) / 100.0 if len(values) >= 24 else 0.0
                
                # Momentum features
                features['fg_momentum'] = self._calculate_momentum(values)
                features['fg_acceleration'] = self._calculate_acceleration(values)
                
                # Regime detection features
                features['fg_extreme_fear'] = 1.0 if current_fg <= 25 else 0.0
                features['fg_fear'] = 1.0 if 25 < current_fg <= 45 else 0.0
                features['fg_neutral'] = 1.0 if 45 < current_fg <= 55 else 0.0
                features['fg_greed'] = 1.0 if 55 < current_fg <= 75 else 0.0
                features['fg_extreme_greed'] = 1.0 if current_fg > 75 else 0.0
                
                # Reversal signals
                features['fg_reversal_signal'] = self._detect_reversal_signal(values)
                features['fg_divergence'] = self._calculate_divergence(values, market_data)
                
            else:
                # Default values for insufficient history
                default_features = {
                    'fg_trend_3h': 0.0, 'fg_trend_12h': 0.0, 'fg_trend_24h': 0.0,
                    'fg_volatility_3h': 0.0, 'fg_volatility_12h': 0.0, 'fg_volatility_24h': 0.0,
                    'fg_momentum': 0.0, 'fg_acceleration': 0.0,
                    'fg_extreme_fear': 0.0, 'fg_fear': 0.0, 'fg_neutral': 1.0, 'fg_greed': 0.0, 'fg_extreme_greed': 0.0,
                    'fg_reversal_signal': 0.0, 'fg_divergence': 0.0
                }
                features.update(default_features)
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting Fear & Greed features: {e}")
            return self._get_default_fg_features()
    
    async def extract_etherscan_features(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract Etherscan balance change momentum and network activity features"""
        try:
            live_aggregated = market_data.get('live_aggregated', {})
            eth_data = live_aggregated.get('ethereum_data', {})
            
            # Current Etherscan metrics
            current_metrics = {
                'gas_price': eth_data.get('gas_price', 20.0),
                'network_utilization': eth_data.get('network_utilization', 0.5),
                'active_addresses': eth_data.get('active_addresses', 500000),
                'transaction_count': eth_data.get('transaction_count', 1000000),
                'whale_activity': eth_data.get('whale_activity', 0.0),
                'defi_tvl': eth_data.get('defi_tvl_change', 0.0),
                'exchange_flows': eth_data.get('exchange_flows', 0.0)
            }
            
            # Store current metrics
            self.etherscan_history.append({
                'timestamp': datetime.now(timezone.utc),
                'metrics': current_metrics
            })
            
            features = {}
            
            # Current normalized features
            features['eth_gas_price_norm'] = min(1.0, current_metrics['gas_price'] / 100.0)
            features['eth_network_util'] = current_metrics['network_utilization']
            features['eth_active_addresses_norm'] = current_metrics['active_addresses'] / 1000000.0
            features['eth_tx_count_norm'] = current_metrics['transaction_count'] / 2000000.0
            features['eth_whale_activity'] = current_metrics['whale_activity']
            
            # Momentum and trend features
            if len(self.etherscan_history) >= 3:
                features.update(self._calculate_etherscan_momentum())
                features.update(self._calculate_etherscan_trends())
                features.update(self._calculate_network_health_score())
            else:
                # Default momentum features
                default_momentum = {
                    'eth_gas_momentum': 0.0, 'eth_address_momentum': 0.0, 'eth_tx_momentum': 0.0,
                    'eth_whale_momentum': 0.0, 'eth_defi_momentum': 0.0,
                    'eth_network_trend': 0.0, 'eth_adoption_trend': 0.0,
                    'eth_network_health': 0.5
                }
                features.update(default_momentum)
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting Etherscan features: {e}")
            return self._get_default_etherscan_features()
    
    async def extract_glassnode_features(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract Glassnode metric trend analysis features"""
        try:
            live_aggregated = market_data.get('live_aggregated', {})
            
            # Bitcoin on-chain metrics
            btc_data = live_aggregated.get('bitcoin_data', {})
            current_btc_metrics = {
                'hash_rate': btc_data.get('hash_rate', 300e18),
                'difficulty': btc_data.get('difficulty_adjustment', 0.0),
                'mempool_size': btc_data.get('mempool_size', 50),
                'active_addresses': btc_data.get('active_addresses', 800000),
                'hodl_waves': btc_data.get('hodl_waves', 0.6),
                'exchange_flows': btc_data.get('exchange_flows', 0.0)
            }
            
            # DeFi and institutional metrics
            defi_data = live_aggregated.get('defi_data', {})
            institutional_data = live_aggregated.get('institutional_data', {})
            
            current_glassnode_metrics = {
                **current_btc_metrics,
                'defi_tvl': defi_data.get('total_value_locked', 50e9),
                'yield_farming_apy': defi_data.get('yield_farming_apy', 0.05),
                'liquidation_risk': defi_data.get('liquidation_risk', 0.1),
                'etf_flows': institutional_data.get('etf_flows', 0.0),
                'institutional_sentiment': institutional_data.get('institutional_sentiment', 0.0)
            }
            
            # Store current metrics
            self.glassnode_history.append({
                'timestamp': datetime.now(timezone.utc),
                'metrics': current_glassnode_metrics
            })
            
            features = {}
            
            # Current normalized features
            features['btc_hash_rate_norm'] = current_btc_metrics['hash_rate'] / 400e18
            features['btc_mempool_norm'] = min(1.0, current_btc_metrics['mempool_size'] / 100.0)
            features['btc_active_addresses_norm'] = current_btc_metrics['active_addresses'] / 1000000.0
            features['btc_hodl_waves'] = current_btc_metrics['hodl_waves']
            features['defi_tvl_norm'] = current_glassnode_metrics['defi_tvl'] / 100e9
            features['yield_farming_apy'] = current_glassnode_metrics['yield_farming_apy']
            
            # Trend and momentum analysis
            if len(self.glassnode_history) >= 3:
                features.update(self._calculate_glassnode_trends())
                features.update(self._calculate_onchain_momentum())
                features.update(self._calculate_market_maturity_score())
            else:
                # Default trend features
                default_trends = {
                    'btc_network_growth': 0.0, 'btc_adoption_trend': 0.0, 'btc_security_trend': 0.0,
                    'defi_growth_momentum': 0.0, 'institutional_momentum': 0.0,
                    'onchain_bullish_signal': 0.0, 'market_maturity_score': 0.5
                }
                features.update(default_trends)
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting Glassnode features: {e}")
            return self._get_default_glassnode_features()
    
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend using linear regression slope"""
        try:
            if len(values) < 2:
                return 0.0
            
            x = np.arange(len(values))
            y = np.array(values)
            
            # Linear regression
            slope = np.polyfit(x, y, 1)[0]
            
            # Normalize slope
            return max(-1.0, min(1.0, slope / 10.0))
            
        except Exception as e:
            logger.error(f"Error calculating trend: {e}")
            return 0.0
    
    def _calculate_momentum(self, values: List[float]) -> float:
        """Calculate momentum as rate of change"""
        try:
            if len(values) < 2:
                return 0.0
            
            recent_avg = np.mean(values[-3:]) if len(values) >= 3 else values[-1]
            older_avg = np.mean(values[-6:-3]) if len(values) >= 6 else values[0]
            
            momentum = (recent_avg - older_avg) / older_avg if older_avg != 0 else 0.0
            
            return max(-1.0, min(1.0, momentum * 10))
            
        except Exception as e:
            logger.error(f"Error calculating momentum: {e}")
            return 0.0
    
    def _calculate_acceleration(self, values: List[float]) -> float:
        """Calculate acceleration (second derivative)"""
        try:
            if len(values) < 3:
                return 0.0
            
            # Calculate first derivatives
            derivatives = [values[i+1] - values[i] for i in range(len(values)-1)]
            
            if len(derivatives) < 2:
                return 0.0
            
            # Calculate second derivative (acceleration)
            acceleration = derivatives[-1] - derivatives[-2]
            
            return max(-1.0, min(1.0, acceleration / 5.0))
            
        except Exception as e:
            logger.error(f"Error calculating acceleration: {e}")
            return 0.0

    def _detect_reversal_signal(self, values: List[float]) -> float:
        """Detect potential reversal signals in Fear & Greed Index"""
        try:
            if len(values) < 5:
                return 0.0

            current = values[-1]
            recent = values[-3:]

            # Extreme readings with reversal patterns
            if current <= 25 and np.mean(recent) > current:  # Fear with upturn
                return 0.8
            elif current >= 75 and np.mean(recent) < current:  # Greed with downturn
                return -0.8
            elif 25 < current <= 35 and all(v < current for v in values[-3:-1]):  # Recovery from fear
                return 0.5
            elif 65 <= current < 75 and all(v > current for v in values[-3:-1]):  # Pullback from greed
                return -0.5

            return 0.0

        except Exception as e:
            logger.error(f"Error detecting reversal signal: {e}")
            return 0.0

    def _calculate_divergence(self, fg_values: List[float], market_data: Dict[str, Any]) -> float:
        """Calculate divergence between Fear & Greed and price action"""
        try:
            if len(fg_values) < 3:
                return 0.0

            # Get price data for comparison
            price_data = market_data.get('price_data', {})
            if not price_data:
                return 0.0

            # Calculate average price change
            price_changes = []
            for symbol_data in price_data.values():
                if 'price_change_24h' in symbol_data:
                    price_changes.append(float(symbol_data['price_change_24h']))

            if not price_changes:
                return 0.0

            avg_price_change = np.mean(price_changes)
            fg_change = fg_values[-1] - fg_values[-3]

            # Normalize changes
            normalized_price_change = avg_price_change / 100.0  # Assuming percentage
            normalized_fg_change = fg_change / 100.0

            # Calculate divergence
            divergence = abs(normalized_price_change - normalized_fg_change)

            return min(1.0, divergence * 2)

        except Exception as e:
            logger.error(f"Error calculating divergence: {e}")
            return 0.0

    def _calculate_etherscan_momentum(self) -> Dict[str, float]:
        """Calculate momentum features from Etherscan data"""
        try:
            if len(self.etherscan_history) < 3:
                return {}

            features = {}

            # Get recent metrics
            recent_metrics = [entry['metrics'] for entry in self.etherscan_history[-3:]]

            # Gas price momentum
            gas_prices = [m['gas_price'] for m in recent_metrics]
            features['eth_gas_momentum'] = self._calculate_momentum(gas_prices)

            # Address activity momentum
            addresses = [m['active_addresses'] for m in recent_metrics]
            features['eth_address_momentum'] = self._calculate_momentum(addresses)

            # Transaction momentum
            tx_counts = [m['transaction_count'] for m in recent_metrics]
            features['eth_tx_momentum'] = self._calculate_momentum(tx_counts)

            # Whale activity momentum
            whale_activity = [m['whale_activity'] for m in recent_metrics]
            features['eth_whale_momentum'] = self._calculate_momentum(whale_activity)

            # DeFi momentum
            defi_changes = [m['defi_tvl'] for m in recent_metrics]
            features['eth_defi_momentum'] = self._calculate_momentum(defi_changes)

            return features

        except Exception as e:
            logger.error(f"Error calculating Etherscan momentum: {e}")
            return {}

    def _calculate_etherscan_trends(self) -> Dict[str, float]:
        """Calculate trend features from Etherscan data"""
        try:
            if len(self.etherscan_history) < 5:
                return {}

            features = {}

            # Network utilization trend
            utilization = [entry['metrics']['network_utilization'] for entry in self.etherscan_history[-12:]]
            features['eth_network_trend'] = self._calculate_trend(utilization)

            # Adoption trend (active addresses)
            addresses = [entry['metrics']['active_addresses'] for entry in self.etherscan_history[-24:]]
            features['eth_adoption_trend'] = self._calculate_trend(addresses)

            return features

        except Exception as e:
            logger.error(f"Error calculating Etherscan trends: {e}")
            return {}

    def _calculate_network_health_score(self) -> Dict[str, float]:
        """Calculate overall network health score"""
        try:
            if len(self.etherscan_history) < 3:
                return {'eth_network_health': 0.5}

            recent_metrics = self.etherscan_history[-1]['metrics']

            # Health components
            gas_health = 1.0 - min(1.0, recent_metrics['gas_price'] / 100.0)  # Lower gas = better
            utilization_health = recent_metrics['network_utilization']  # Higher utilization = better
            activity_health = min(1.0, recent_metrics['active_addresses'] / 1000000.0)  # More addresses = better

            # Weighted health score
            health_score = (gas_health * 0.3 + utilization_health * 0.4 + activity_health * 0.3)

            return {'eth_network_health': health_score}

        except Exception as e:
            logger.error(f"Error calculating network health: {e}")
            return {'eth_network_health': 0.5}

    def _calculate_glassnode_trends(self) -> Dict[str, float]:
        """Calculate trend features from Glassnode data"""
        try:
            if len(self.glassnode_history) < 5:
                return {}

            features = {}

            # Bitcoin network growth trend
            hash_rates = [entry['metrics']['hash_rate'] for entry in self.glassnode_history[-24:]]
            features['btc_network_growth'] = self._calculate_trend(hash_rates)

            # Adoption trend
            addresses = [entry['metrics']['active_addresses'] for entry in self.glassnode_history[-24:]]
            features['btc_adoption_trend'] = self._calculate_trend(addresses)

            # Security trend
            difficulties = [entry['metrics']['difficulty'] for entry in self.glassnode_history[-12:]]
            features['btc_security_trend'] = self._calculate_trend(difficulties)

            return features

        except Exception as e:
            logger.error(f"Error calculating Glassnode trends: {e}")
            return {}

    def _calculate_onchain_momentum(self) -> Dict[str, float]:
        """Calculate on-chain momentum indicators"""
        try:
            if len(self.glassnode_history) < 3:
                return {}

            features = {}

            # DeFi growth momentum
            defi_tvl = [entry['metrics']['defi_tvl'] for entry in self.glassnode_history[-6:]]
            features['defi_growth_momentum'] = self._calculate_momentum(defi_tvl)

            # Institutional momentum
            etf_flows = [entry['metrics']['etf_flows'] for entry in self.glassnode_history[-6:]]
            features['institutional_momentum'] = self._calculate_momentum(etf_flows)

            # On-chain bullish signal
            hodl_waves = [entry['metrics']['hodl_waves'] for entry in self.glassnode_history[-6:]]
            features['onchain_bullish_signal'] = self._calculate_trend(hodl_waves)

            return features

        except Exception as e:
            logger.error(f"Error calculating on-chain momentum: {e}")
            return {}

    def _calculate_market_maturity_score(self) -> Dict[str, float]:
        """Calculate market maturity score based on on-chain metrics"""
        try:
            if len(self.glassnode_history) < 1:
                return {'market_maturity_score': 0.5}

            recent_metrics = self.glassnode_history[-1]['metrics']

            # Maturity components
            network_security = min(1.0, recent_metrics['hash_rate'] / 400e18)
            institutional_adoption = min(1.0, abs(recent_metrics['etf_flows']) / 1e9)
            defi_maturity = min(1.0, recent_metrics['defi_tvl'] / 100e9)
            hodl_behavior = recent_metrics['hodl_waves']

            # Weighted maturity score
            maturity_score = (
                network_security * 0.3 +
                institutional_adoption * 0.25 +
                defi_maturity * 0.25 +
                hodl_behavior * 0.2
            )

            return {'market_maturity_score': maturity_score}

        except Exception as e:
            logger.error(f"Error calculating market maturity: {e}")
            return {'market_maturity_score': 0.5}

    def _get_default_fg_features(self) -> Dict[str, float]:
        """Get default Fear & Greed features"""
        return {
            'fg_current': 0.65, 'fg_normalized': 0.3,
            'fg_trend_3h': 0.0, 'fg_trend_12h': 0.0, 'fg_trend_24h': 0.0,
            'fg_volatility_3h': 0.0, 'fg_volatility_12h': 0.0, 'fg_volatility_24h': 0.0,
            'fg_momentum': 0.0, 'fg_acceleration': 0.0,
            'fg_extreme_fear': 0.0, 'fg_fear': 0.0, 'fg_neutral': 1.0, 'fg_greed': 0.0, 'fg_extreme_greed': 0.0,
            'fg_reversal_signal': 0.0, 'fg_divergence': 0.0
        }

    def _get_default_etherscan_features(self) -> Dict[str, float]:
        """Get default Etherscan features"""
        return {
            'eth_gas_price_norm': 0.2, 'eth_network_util': 0.5, 'eth_active_addresses_norm': 0.5,
            'eth_tx_count_norm': 0.5, 'eth_whale_activity': 0.0,
            'eth_gas_momentum': 0.0, 'eth_address_momentum': 0.0, 'eth_tx_momentum': 0.0,
            'eth_whale_momentum': 0.0, 'eth_defi_momentum': 0.0,
            'eth_network_trend': 0.0, 'eth_adoption_trend': 0.0, 'eth_network_health': 0.5
        }

    def _get_default_glassnode_features(self) -> Dict[str, float]:
        """Get default Glassnode features"""
        return {
            'btc_hash_rate_norm': 0.75, 'btc_mempool_norm': 0.5, 'btc_active_addresses_norm': 0.8,
            'btc_hodl_waves': 0.6, 'defi_tvl_norm': 0.5, 'yield_farming_apy': 0.05,
            'btc_network_growth': 0.0, 'btc_adoption_trend': 0.0, 'btc_security_trend': 0.0,
            'defi_growth_momentum': 0.0, 'institutional_momentum': 0.0,
            'onchain_bullish_signal': 0.0, 'market_maturity_score': 0.5
        }

# Export the main class
__all__ = ['FeatureEngineer']
