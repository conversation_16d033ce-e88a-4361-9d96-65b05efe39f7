#!/usr/bin/env python3
"""
Simple Trader - IMMEDIATE EXECUTION
CRITICAL: This executes real money trades immediately
"""

import os
import sys
import asyncio
import time
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Force Bybit-only environment
os.environ["BYBIT_ONLY_MODE"] = "true"
os.environ["COINBASE_ENABLED"] = "false"
os.environ["LIVE_TRADING"] = "true"
os.environ["REAL_MONEY_TRADING"] = "true"

async def simple_trading():
    """Simple trading with immediate execution"""
    print("⚡ [SIMPLE] Starting simple trading system...")
    
    try:
        # Import essential components
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Get API credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            raise RuntimeError("CRITICAL: Missing Bybit API credentials")
        
        print("🔧 [SIMPLE] Initializing Bybit client...")
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False  # REAL MONEY TRADING ONLY
        )
        
        if not bybit_client.session:
            raise RuntimeError("CRITICAL: Failed to initialize Bybit client")
        
        print("✅ [SIMPLE] Bybit client initialized successfully")
        
        # Execute one trade immediately
        print("🎯 [SIMPLE] Executing immediate trade...")
        
        # Get current balances
        balances = await bybit_client.get_all_available_balances()
        usdt_balance = balances.get('USDT', 0)
        
        print(f"💰 [BALANCE] USDT: ${usdt_balance:.2f}")
        
        if usdt_balance >= 5.0:
            # Execute immediate buy
            success = await execute_immediate_buy(bybit_client, usdt_balance)
            if success:
                print("✅ [SIMPLE] Trade executed successfully!")
                return 0
            else:
                print("❌ [SIMPLE] Trade failed")
                return 1
        else:
            print(f"⚠️ [BALANCE] Insufficient USDT: ${usdt_balance:.2f} < $5.00")
            return 1
                
    except Exception as e:
        print(f"❌ [SIMPLE] Critical error: {e}")
        import traceback
        traceback.print_exc()
        return 1

async def execute_immediate_buy(client, usdt_balance):
    """Execute an immediate buy order"""
    try:
        # Choose SOL for this trade
        symbol = "SOLUSDT"
        
        print(f"🎯 [BUY] Targeting SOL...")
        
        # Get current price
        current_price = await client.get_fast_price(symbol)
        if not current_price or current_price <= 0:
            print(f"❌ [BUY] Failed to get price for {symbol}")
            return False
        
        print(f"💹 [PRICE] {symbol}: ${current_price:.4f}")
        
        # Calculate trade amount (25% of USDT balance, max $10)
        trade_amount = min(usdt_balance * 0.25, 10.0)
        
        if trade_amount < 5.0:
            print(f"⚠️ [BUY] Trade amount too small: ${trade_amount:.2f}")
            return False
        
        print(f"💰 [BUY] Trade amount: ${trade_amount:.2f}")
        
        # Calculate quantity
        quantity = trade_amount / current_price
        
        print(f"📊 [BUY] Quantity: {quantity:.6f} SOL")
        
        # Execute the buy order using the existing place_order method
        result = await client.place_order(
            symbol=symbol,
            side='Buy',
            amount=trade_amount,  # Use USDT amount
            price=None,  # Market price
            order_type='market',  # Market order for immediate execution
            is_quote_amount=True  # Amount is in USDT (quote currency)
        )
        
        # Check if order was successful (status 'filled' or has order_id)
        if result and (result.get('status') == 'filled' or result.get('order_id')):
            print(f"✅ [BUY] Successfully bought SOL for ${trade_amount:.2f}")
            print(f"📋 [ORDER] Order ID: {result.get('order_id', 'N/A')}")
            print(f"📊 [FILLED] Quantity: {result.get('filled_qty', 'N/A')} SOL")
            print(f"💹 [PRICE] Average Price: ${result.get('avg_price', 'N/A')}")
            return True
        else:
            print(f"❌ [BUY] Failed to buy SOL: {result}")
            return False
            
    except Exception as e:
        print(f"❌ [BUY] Error executing buy: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("⚡ [SIMPLE-TRADER] Simple trading system starting...")
    print("🎯 [SIMPLE-TRADER] IMMEDIATE EXECUTION - REAL MONEY TRADING")
    print("💰 [SIMPLE-TRADER] Bybit only - Market orders")
    
    try:
        exit_code = asyncio.run(simple_trading())
        print(f"\n🏁 [SIMPLE-TRADER] System finished with exit code: {exit_code}")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 [SIMPLE-TRADER] System stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ [SIMPLE-TRADER] System failed: {e}")
        sys.exit(1)
