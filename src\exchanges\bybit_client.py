from pybit.unified_trading import <PERSON>TT<PERSON>
import logging
from decimal import Decimal
from typing import Optional, Dict
import asyncio
import time

# Import speed optimization components
from ..performance.speed_optimizer import (
    fast_api_call, fast_balance_validation, cached_market_data, speed_optimizer
)
from .high_speed_connection_pool import fast_http_request, connection_pool

logger = logging.getLogger(__name__)


class BybitClient:
    def __init__(self, api_key: str, api_secret: str, testnet: bool = False):
        """Initialize Bybit client with API credentials."""
        # Check if the API key is encrypted and needs decryption
        if api_key and isinstance(api_key, str) and api_key.startswith("gAAAAA"):
            try:
                from src.utils.cryptography.hybrid import HybridCrypto

                crypto = HybridCrypto()
                api_key = crypto.decrypt_value(api_key)
                api_secret = crypto.decrypt_value(api_secret)
                logger.info("Successfully decrypted Bybit API credentials")
            except Exception as e:
                logger.error(f"Error decrypting Bybit credentials: {e}")
                # Continue with the encrypted key as fallback
        self.session = HTTP(api_key=api_key, api_secret=api_secret, testnet=testnet)
        self.name = "bybit"

    def _normalize_symbol(self, symbol: str) -> str:
        """Convert various symbol formats to Bybit's format"""
        if not symbol:
            return "BTCUSDT"  # Default symbol
        if "-" in symbol:
            base, quote = symbol.split("-")
            if quote.upper() == "USD":
                quote = "USDT"
            return f"{base}{quote}".upper()
        return symbol.upper()

    def get_price(self, symbol: str) -> Decimal:
        """Get the current market price for a symbol. e.g., 'BTCUSDT'"""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            logger.info(
                f"Getting price for {symbol} (normalized to {normalized_symbol})"
            )
            response = self.session.get_tickers(
                category="spot", symbol=normalized_symbol
            )
            if response and response["retCode"] == 0 and response["result"]["list"]:
                return Decimal(response["result"]["list"][0]["lastPrice"])
            logger.warning(
                f"Bybit: Could not get price for {normalized_symbol}. Response: {response}"
            )
            return Decimal("0.0")
        except Exception as e:
            logger.error(f"Bybit: Error getting price for {symbol}: {e}")
            raise

    def get_volume(self, symbol: str) -> Decimal:
        """Get the 24-hour trading volume for a symbol."""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            response = self.session.get_tickers(
                category="spot", symbol=normalized_symbol
            )
            if response and response["retCode"] == 0 and response["result"]["list"]:
                return Decimal(response["result"]["list"][0]["volume24h"])
            logger.warning(
                f"Bybit: Could not get volume for {normalized_symbol}. Response: {response}"
            )
            return Decimal("0.0")
        except Exception as e:
            logger.error(f"Bybit: Error getting volume for {symbol}: {e}")
            raise

    def buy(
        self,
        symbol: str,
        amount_base: Decimal,
        price: Decimal = None,
        order_type: str = "Market",
    ) -> dict:
        """Place a buy order. Amount is in base currency (e.g., BTC for BTCUSDT)."""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            logger.info(
                f"Placing buy order for {symbol} (normalized to {normalized_symbol})"
            )
            params = {
                "category": "spot",
                "symbol": normalized_symbol,
                "side": "Buy",
                "orderType": order_type,
                "qty": str(amount_base),
            }
            if order_type == "Limit" and price:
                params["price"] = str(price)
            order_result = self.session.place_order(**params)
            logger.info(f"Bybit: Buy order placed for {symbol}: {order_result}")
            return order_result
        except Exception as e:
            logger.error(f"Bybit: Error placing buy order for {symbol}: {e}")
            raise

    def sell(
        self,
        symbol: str,
        amount_base: Decimal,
        price: Decimal = None,
        order_type: str = "Market",
    ) -> dict:
        """Place a sell order. Amount is in base currency."""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            logger.info(
                f"Placing sell order for {symbol} (normalized to {normalized_symbol})"
            )
            params = {
                "category": "spot",
                "symbol": normalized_symbol,
                "side": "Sell",
                "orderType": order_type,
                "qty": str(amount_base),
            }
            if order_type == "Limit" and price:
                params["price"] = str(price)
            order_result = self.session.place_order(**params)
            logger.info(f"Bybit: Sell order placed for {symbol}: {order_result}")
            return order_result
        except Exception as e:
            logger.error(f"Bybit: Error placing sell order for {symbol}: {e}")
            raise

    def get_market_state(self, symbol: str) -> dict:
        """Get a snapshot of the market state."""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            logger.info(
                f"Getting market state for {symbol} (normalized to {normalized_symbol})"
            )
            response = self.session.get_tickers(
                category="spot", symbol=normalized_symbol
            )
            if response and response["retCode"] == 0 and response["result"]["list"]:
                ticker_data = response["result"]["list"][0]
                return {
                    "price": Decimal(ticker_data.get("lastPrice", "0")),
                    "bid": Decimal(ticker_data.get("bid1Price", "0")),
                    "ask": Decimal(ticker_data.get("ask1Price", "0")),
                    "volume": Decimal(ticker_data.get("volume24h", "0")),
                    "high_24h": Decimal(ticker_data.get("highPrice24h", "0")),
                    "low_24h": Decimal(ticker_data.get("lowPrice24h", "0")),
                    "open_24h": Decimal(ticker_data.get("prevPrice24h", "0")),
                    "last_trade_size": Decimal(ticker_data.get("lastTradeQty", "0")),
                }
            return {}
        except Exception as e:
            logger.error(f"Bybit: Error getting market state for {symbol}: {e}")
            return {}

    def get_ticker(self, symbol: str = "BTCUSDT") -> dict:
        """Get product ticker."""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            logger.info(
                f"Getting ticker for {symbol} (normalized to {normalized_symbol})"
            )
            response = self.session.get_tickers(
                category="spot", symbol=normalized_symbol
            )
            if response and response["retCode"] == 0 and response["result"]["list"]:
                return response["result"]["list"][0]
            return {}
        except Exception as e:
            logger.error(f"Bybit: Error getting ticker for {symbol}: {e}")
            return {}

    @fast_balance_validation
    async def get_balance(self, coin: str = "USDT") -> Decimal:
        """Get account balance for a specific coin with speed optimization."""
        try:
            # Use async wrapper for speed
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.session.get_wallet_balance(accountType="UNIFIED", coin=coin)
            )

            if response and response["retCode"] == 0 and response["result"]["list"]:
                for account in response["result"]["list"]:
                    for c in account.get("coin", []):
                        if c["coin"] == coin:
                            return Decimal(c["walletBalance"])
            return Decimal("0.0")
        except Exception as e:
            logger.error(f"Bybit: Error getting balance for {coin}: {e}")
            raise

    @cached_market_data(ttl_seconds=5.0)
    async def get_all_balances(self) -> Dict[str, Decimal]:
        """
        Get ALL balances from Bybit account across all account types.
        Returns dictionary with currency -> balance mapping for ALL currencies with any balance.
        """
        try:
            all_balances = {}

            # Check ALL account types for comprehensive detection
            account_types = ["UNIFIED", "SPOT", "FUNDING", "CONTRACT"]

            # Use parallel requests for speed
            async def get_account_balance(account_type):
                try:
                    logger.debug(f"Checking Bybit {account_type} account for all currencies...")
                    response = await asyncio.get_event_loop().run_in_executor(
                        None,
                        lambda: self.session.get_wallet_balance(accountType=account_type)
                    )
                    return account_type, response
                except Exception as e:
                    logger.debug(f"Could not check Bybit {account_type} account: {e}")
                    return account_type, None

            # Execute all requests in parallel for speed
            tasks = [get_account_balance(account_type) for account_type in account_types]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            for account_type, response in results:
                if isinstance(response, Exception):
                    continue
                try:

                    if response and response.get('retCode') == 0:
                        for account_balance_info in response['result'].get('list', []):
                            for coin_info in account_balance_info.get('coin', []):
                                coin_symbol = coin_info.get('coin')

                                # Get all balance types
                                wallet_balance = Decimal(coin_info.get('walletBalance', '0'))
                                available_balance = Decimal(coin_info.get('availableToWithdraw', '0'))
                                equity = Decimal(coin_info.get('equity', '0'))

                                # Use the highest available balance
                                max_balance = max(wallet_balance, available_balance, equity)

                                # Record ANY currency with ANY balance > 0
                                if max_balance > Decimal('0'):
                                    existing_balance = all_balances.get(coin_symbol.upper(), Decimal('0'))
                                    all_balances[coin_symbol.upper()] = max(existing_balance, max_balance)
                                    logger.debug(f"Bybit {account_type} detected {coin_symbol}: {max_balance}")

                except Exception as e:
                    logger.debug(f"Could not process Bybit {account_type} response: {e}")
                    continue

            # Log summary of detected currencies
            if all_balances:
                logger.info(f"Bybit TOTAL detected currencies: {len(all_balances)} - {list(all_balances.keys())}")
                for currency, balance in all_balances.items():
                    logger.info(f"  {currency}: {balance}")
            else:
                logger.warning("Bybit: No currencies with balances detected")

            return all_balances

        except Exception as e:
            logger.error(f"Bybit: Error getting all balances: {e}")
            return {}

    def withdraw_to_crypto_address(
        self,
        coin: str,
        amount: Decimal,
        address: str,
        chain: str,
        tag: Optional[str] = None,
        **kwargs,
    ) -> dict:
        """
        Withdraws crypto to an external address.
        Uses the underlying pybit client's withdraw method.
        """
        try:
            params = {
                "coin": coin.upper(),
                "chain": chain,
                "address": address,
                "amount": str(amount),
            }
            if tag:
                params["tag"] = tag
            params.update(kwargs)
            logger.info(
                f"BybitClient: Attempting to withdraw {amount} {coin} to {address} on chain {chain} with params {params}"
            )
            result = self.session.withdraw(params)
            logger.info(f"BybitClient: Crypto withdrawal result: {result}")
            return result
        except Exception as e:
            logger.error(
                f"BybitClient: Error during crypto withdrawal for {coin} to {address}: {e}"
            )
            raise

    def close(self):
        logger.info("BybitClient: No explicit close method for pybit HTTP session.")
        pass
