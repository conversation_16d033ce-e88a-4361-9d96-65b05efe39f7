"""
Graph Neural Network for Market Relationship Modeling
Models correlations and dependencies between different assets and market factors
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class GraphConfig:
    """Enhanced configuration for graph neural network with sentiment and on-chain data"""
    node_features: int = 64
    edge_features: int = 32
    hidden_dim: int = 128
    num_layers: int = 4  # Increased for enhanced complexity
    num_heads: int = 6   # Increased for better attention
    dropout: float = 0.1
    aggregation: str = 'attention'  # Use attention for better performance

    # Enhanced node type configurations
    asset_node_features: int = 64      # Traditional asset features
    sentiment_node_features: int = 32   # Sentiment data features
    onchain_node_features: int = 48     # On-chain data features
    macro_node_features: int = 24       # Macro economic features

    # Node type weights for heterogeneous graph
    node_type_weights: Dict[str, float] = None

    def __post_init__(self):
        if self.node_type_weights is None:
            self.node_type_weights = {
                'asset': 1.0,
                'sentiment': 0.8,
                'onchain': 0.9,
                'macro': 0.7
            }

class GraphAttentionLayer(nn.Module):
    """Graph Attention Layer for learning node relationships"""
    
    def __init__(self, in_features: int, out_features: int, num_heads: int = 1, 
                 dropout: float = 0.1, alpha: float = 0.2):
        super().__init__()
        
        self.in_features = in_features
        self.out_features = out_features
        self.num_heads = num_heads
        self.dropout = dropout
        self.alpha = alpha
        
        # Linear transformations for each head
        self.W = nn.ModuleList([
            nn.Linear(in_features, out_features, bias=False) 
            for _ in range(num_heads)
        ])
        
        # Attention mechanism
        self.a = nn.ModuleList([
            nn.Linear(2 * out_features, 1, bias=False) 
            for _ in range(num_heads)
        ])
        
        self.dropout_layer = nn.Dropout(dropout)
        self.leaky_relu = nn.LeakyReLU(alpha)
        
    def forward(self, h: torch.Tensor, adj: torch.Tensor) -> torch.Tensor:
        batch_size, num_nodes, _ = h.shape
        
        # Multi-head attention
        head_outputs = []
        
        for head in range(self.num_heads):
            # Linear transformation
            Wh = self.W[head](h)  # [batch, num_nodes, out_features]
            
            # Compute attention coefficients
            attention_input = self._prepare_attention_input(h, adj_matrix)
            e = self.a[head](attention_input).squeeze(-1)  # [batch, num_nodes, num_nodes]
            
            # Mask attention with adjacency matrix
            e = e.masked_fill(adj == 0, -1e9)
            
            # Apply softmax
            attention = F.softmax(e, dim=-1)
            attention = self.dropout_layer(attention)
            
            # Apply attention to features
            h_prime = torch.bmm(attention, Wh)  # [batch, num_nodes, out_features]
            head_outputs.append(h_prime)
        
        # Concatenate or average heads
        if self.num_heads == 1:
            output = head_outputs[0]
        else:
            output = torch.cat(head_outputs, dim=-1)
        
        return self.leaky_relu(output)
    
    def _prepare_attention_input(self, Wh: torch.Tensor, adj_matrix: torch.Tensor) -> torch.Tensor:
        batch_size, num_nodes, out_features = Wh.shape
        
        # Create all pairs for attention computation
        Wh_repeated_in_chunks = Wh.repeat_interleave(num_nodes, dim=1)
        Wh_repeated_alternating = Wh.repeat(1, num_nodes, 1)
        
        # Concatenate to get input for attention
        all_combinations_matrix = torch.cat([
            Wh_repeated_in_chunks, Wh_repeated_alternating
        ], dim=-1)
        
        return all_combinations_matrix.view(batch_size, num_nodes, num_nodes, 2 * out_features)

class GraphConvolutionLayer(nn.Module):
    """Graph Convolution Layer for message passing"""
    
    def __init__(self, in_features: int, out_features: int, bias: bool = True):
        super().__init__()
        
        self.in_features = in_features
        self.out_features = out_features
        
        self.weight = nn.Parameter(torch.FloatTensor(in_features, out_features))
        if bias:
            self.bias = nn.Parameter(torch.FloatTensor(out_features))
        else:
            self.register_parameter('bias', None)
        
        self.reset_parameters()
    
    def reset_parameters(self):
        nn.init.xavier_uniform_(self.weight)
        if self.bias is not None:
            nn.init.zeros_(self.bias)
    
    def forward(self, input: torch.Tensor, adj: torch.Tensor) -> torch.Tensor:
        # Linear transformation
        support = torch.matmul(input, self.weight)
        
        # Graph convolution: A * X * W
        output = torch.bmm(adj, support)
        
        if self.bias is not None:
            output = output + self.bias
        
        return output

class MarketGraphBuilder:
    """Builds graph structure from market data"""
    
    def __init__(self, correlation_threshold: float = 0.3):
        self.correlation_threshold = correlation_threshold
        self.asset_to_idx = {}
        self.idx_to_asset = {}
        
    def build_correlation_graph(self, price_data: Dict[str, np.ndarray]) -> Tuple[torch.Tensor, Dict]:
        """Build graph based on price correlations"""
        assets = list(price_data.keys())
        num_assets = len(assets)
        
        # Create asset mappings
        self.asset_to_idx = {asset: idx for idx, asset in enumerate(assets)}
        self.idx_to_asset = {idx: asset for asset, idx in self.asset_to_idx.items()}
        
        # Calculate correlation matrix
        price_matrix = np.array([price_data[asset] for asset in assets])
        correlation_matrix = np.corrcoef(price_matrix)
        
        # Create adjacency matrix based on correlation threshold
        adj_matrix = np.abs(correlation_matrix) > self.correlation_threshold
        np.fill_diagonal(adj_matrix, True)  # Self-connections
        
        # Convert to tensor
        adj_tensor = torch.FloatTensor(adj_matrix).unsqueeze(0)
        
        graph_info = {
            'assets': assets,
            'correlation_matrix': correlation_matrix,
            'num_nodes': num_assets,
            'edge_count': np.sum(adj_matrix) - num_assets  # Exclude self-connections
        }
        
        return adj_tensor, graph_info
    
    def build_sector_graph(self, asset_sectors: Dict[str, str]) -> Tuple[torch.Tensor, Dict]:
        """Build graph based on sector relationships"""
        assets = list(asset_sectors.keys())
        num_assets = len(assets)
        
        # Create asset mappings
        self.asset_to_idx = {asset: idx for idx, asset in enumerate(assets)}
        self.idx_to_asset = {idx: asset for asset, idx in self.asset_to_idx.items()}
        
        # Create adjacency matrix based on sectors
        adj_matrix = np.zeros((num_assets, num_assets))
        
        for i, asset1 in enumerate(assets):
            for j, asset2 in enumerate(assets):
                if i == j:
                    adj_matrix[i, j] = 1.0  # Self-connection
                elif asset_sectors[asset1] == asset_sectors[asset2]:
                    adj_matrix[i, j] = 1.0  # Same sector
        
        adj_tensor = torch.FloatTensor(adj_matrix).unsqueeze(0)
        
        graph_info = {
            'assets': assets,
            'sectors': asset_sectors,
            'num_nodes': num_assets,
            'edge_count': np.sum(adj_matrix) - num_assets
        }
        
        return adj_tensor, graph_info

class MarketGraphNeuralNetwork(nn.Module):
    """
    Enhanced Graph Neural Network for modeling market relationships with sentiment and on-chain data
    Features:
    - Graph attention layers for learning asset relationships
    - Graph convolution for message passing
    - Multi-scale temporal modeling
    - Portfolio-level predictions
    - Sentiment and on-chain data integration
    - Heterogeneous node type support
    """

    def __init__(self, config: GraphConfig, num_assets: int, num_sentiment_nodes: int = 5,
                 num_onchain_nodes: int = 8, num_macro_nodes: int = 3):
        super().__init__()

        self.config = config
        self.num_assets = num_assets
        self.num_sentiment_nodes = num_sentiment_nodes
        self.num_onchain_nodes = num_onchain_nodes
        self.num_macro_nodes = num_macro_nodes
        self.total_nodes = num_assets + num_sentiment_nodes + num_onchain_nodes + num_macro_nodes

        # Heterogeneous node embeddings for different node types
        self.asset_embedding = nn.Linear(config.asset_node_features, config.hidden_dim)
        self.sentiment_embedding = nn.Linear(config.sentiment_node_features, config.hidden_dim)
        self.onchain_embedding = nn.Linear(config.onchain_node_features, config.hidden_dim)
        self.macro_embedding = nn.Linear(config.macro_node_features, config.hidden_dim)

        # Node type embeddings for heterogeneous graph learning
        self.node_type_embedding = nn.Embedding(4, config.hidden_dim // 4)  # 4 node types
        
        # Graph layers
        self.graph_layers = nn.ModuleList()
        
        for i in range(config.num_layers):
            if i == 0:
                in_dim = config.hidden_dim
            else:
                in_dim = config.hidden_dim * config.num_heads if config.num_heads > 1 else config.hidden_dim
            
            # Alternate between attention and convolution layers
            if i % 2 == 0:
                layer = GraphAttentionLayer(
                    in_dim, config.hidden_dim, config.num_heads, config.dropout
                )
            else:
                layer = GraphConvolutionLayer(in_dim, config.hidden_dim)
            
            self.graph_layers.append(layer)
        
        # Output layers
        final_dim = config.hidden_dim * config.num_heads if config.num_heads > 1 else config.hidden_dim
        
        self.node_predictor = nn.Sequential(
            nn.Linear(final_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, 1)
        )
        
        self.graph_predictor = nn.Sequential(
            nn.Linear(final_dim * num_assets, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, 1)
        )
        
        # Portfolio optimization head
        self.portfolio_head = nn.Sequential(
            nn.Linear(final_dim * num_assets, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim, num_assets),
            nn.Softmax(dim=-1)
        )
        
    def forward(self, node_features: torch.Tensor, adj_matrix: torch.Tensor) -> Dict[str, torch.Tensor]:
        batch_size = node_features.size(0)
        
        # Initial node embedding
        h = self.node_embedding(node_features)
        
        # Apply graph layers
        for i, layer in enumerate(self.graph_layers):
            if isinstance(layer, GraphAttentionLayer):
                h = layer(h, adj_matrix)
            else:  # GraphConvolutionLayer
                h = layer(h, adj_matrix)
            
            # Apply activation and dropout (except for last layer)
            if i < len(self.graph_layers) - 1:
                h = F.relu(h)
                h = F.dropout(h, p=self.config.dropout, training=self.training)
        
        # Node-level predictions
        node_predictions = self.node_predictor(h)
        
        # Graph-level prediction
        graph_representation = h.view(batch_size, -1)
        graph_prediction = self.graph_predictor(graph_representation)
        
        # Portfolio weights
        portfolio_weights = self.portfolio_head(graph_representation)
        
        return {
            'node_predictions': node_predictions,
            'graph_prediction': graph_prediction,
            'portfolio_weights': portfolio_weights,
            'node_embeddings': h,
            'graph_representation': graph_representation
        }
    
    def predict_correlations(self, node_features: torch.Tensor, adj_matrix: torch.Tensor) -> torch.Tensor:
        """Predict future correlations between assets"""
        outputs = self.forward(node_features, adj_matrix)
        embeddings = outputs['node_embeddings']
        
        # Compute pairwise similarities
        batch_size, num_nodes, embed_dim = embeddings.shape
        
        # Normalize embeddings
        embeddings_norm = F.normalize(embeddings, p=2, dim=-1)
        
        # Compute correlation matrix
        correlation_matrix = torch.bmm(embeddings_norm, embeddings_norm.transpose(1, 2))
        
        return correlation_matrix
    
    def get_attention_weights(self, node_features: torch.Tensor, adj_matrix: torch.Tensor) -> List[torch.Tensor]:
        """Extract attention weights for interpretability"""
        attention_weights = []
        
        h = self.node_embedding(node_features)
        
        for layer in self.graph_layers:
            if isinstance(layer, GraphAttentionLayer):
                # Modified forward pass to return attention weights
                batch_size, num_nodes, _ = h.shape
                head_attentions = []
                
                for head in range(layer.num_heads):
                    Wh = layer.W[head](h)
                    attention_input = layer._prepare_attention_input(Wh, adj_matrix)
                    e = layer.a[head](attention_input).squeeze(-1)
                    e = e.masked_fill(adj_matrix == 0, -1e9)
                    attention = F.softmax(e, dim=-1)
                    head_attentions.append(attention)
                
                # Average attention across heads
                avg_attention = torch.stack(head_attentions).mean(dim=0)
                attention_weights.append(avg_attention)
                
                h = layer(h, adj_matrix)
            else:
                h = layer(h, adj_matrix)
        
        return attention_weights
    
    def analyze_market_structure(self, node_features: torch.Tensor, adj_matrix: torch.Tensor) -> Dict[str, Any]:
        """Analyze learned market structure"""
        outputs = self.forward(node_features, adj_matrix)
        attention_weights = self.get_attention_weights(node_features, adj_matrix)
        
        # Calculate centrality measures
        embeddings = outputs['node_embeddings']
        centrality_scores = torch.norm(embeddings, p=2, dim=-1)
        
        # Identify market leaders (highest centrality)
        market_leaders = torch.argsort(centrality_scores, dim=-1, descending=True)
        
        # Calculate clustering coefficient
        predicted_correlations = self.predict_correlations(node_features, adj_matrix)
        
        analysis = {
            'centrality_scores': centrality_scores.cpu().numpy(),
            'market_leaders': market_leaders.cpu().numpy(),
            'predicted_correlations': predicted_correlations.cpu().numpy(),
            'attention_patterns': [attn.cpu().numpy() for attn in attention_weights],
            'portfolio_weights': outputs['portfolio_weights'].cpu().numpy()
        }
        
        return analysis
    
    def save_model(self, path: str):
        """Save model with configuration"""
        torch.save({
            'model_state_dict': self.state_dict(),
            'config': self.config,
            'num_assets': self.num_assets
        }, path)
        logger.info(f"Graph neural network saved to {path}")
    
    def load_model(self, path: str):
        """Load model with configuration"""
        checkpoint = torch.load(path, map_location='cpu')
        self.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"Graph neural network loaded from {path}")

    def create_sentiment_nodes(self, market_data: Dict[str, Any],
                             sentiment_data: Optional[Dict[str, Any]] = None) -> torch.Tensor:
        """Create sentiment data nodes for the graph"""
        try:
            sentiment_features = []

            # Node 1: Fear & Greed Index node
            live_aggregated = market_data.get('live_aggregated', {})
            fear_greed_index = live_aggregated.get('fear_greed_index', 65.0) / 100.0  # Normalize
            fear_greed_features = [
                fear_greed_index,
                (fear_greed_index - 0.5) * 2,  # Centered around 0
                1.0 if fear_greed_index < 0.3 else 0.0,  # Extreme fear flag
                1.0 if fear_greed_index > 0.7 else 0.0,  # Extreme greed flag
                abs(fear_greed_index - 0.5) * 2,  # Extremeness measure
            ]

            # Node 2: Web sentiment node
            web_insights = market_data.get('web_crawler_insights', {})
            web_sentiment = web_insights.get('overall_sentiment', 0.0)
            high_impact_count = web_insights.get('high_impact_count', 0)
            web_features = [
                web_sentiment,
                abs(web_sentiment),  # Sentiment strength
                1.0 if web_sentiment > 0.5 else 0.0,  # Positive sentiment flag
                1.0 if web_sentiment < -0.5 else 0.0,  # Negative sentiment flag
                min(1.0, high_impact_count / 10.0),  # Normalized impact count
            ]

            # Node 3: Aggregated sentiment node
            if sentiment_data:
                agg_sentiment = sentiment_data.get('aggregated_sentiment', 0.0)
                confidence = sentiment_data.get('confidence', 0.0)
                signal_strength = sentiment_data.get('signal_strength', 0.0)
                agg_features = [
                    agg_sentiment,
                    confidence,
                    signal_strength,
                    agg_sentiment * confidence,  # Weighted sentiment
                    1.0 if confidence > 0.7 else 0.0,  # High confidence flag
                ]
            else:
                agg_features = [0.0] * 5

            # Node 4: Market volatility sentiment
            price_data = market_data.get('price_data', {})
            volatilities = [
                float(data.get('volatility', 0.1))
                for data in price_data.values()
                if 'volatility' in data
            ]
            avg_volatility = np.mean(volatilities) if volatilities else 0.1
            vol_features = [
                avg_volatility,
                min(1.0, avg_volatility / 0.2),  # Normalized volatility
                1.0 if avg_volatility > 0.15 else 0.0,  # High volatility flag
                1.0 if avg_volatility < 0.05 else 0.0,  # Low volatility flag
                np.std(volatilities) if len(volatilities) > 1 else 0.0,  # Volatility spread
            ]

            # Node 5: Composite sentiment node
            composite_sentiment = (fear_greed_index - 0.5) * 0.4 + web_sentiment * 0.3 + agg_sentiment * 0.3
            composite_features = [
                composite_sentiment,
                abs(composite_sentiment),
                composite_sentiment * (1 - avg_volatility),  # Risk-adjusted sentiment
                1.0 if composite_sentiment > 0.3 else 0.0,  # Bullish flag
                1.0 if composite_sentiment < -0.3 else 0.0,  # Bearish flag
            ]

            # Combine all sentiment features
            sentiment_features = [
                fear_greed_features + [0.0] * (self.config.sentiment_node_features - 5),
                web_features + [0.0] * (self.config.sentiment_node_features - 5),
                agg_features + [0.0] * (self.config.sentiment_node_features - 5),
                vol_features + [0.0] * (self.config.sentiment_node_features - 5),
                composite_features + [0.0] * (self.config.sentiment_node_features - 5)
            ]

            # Pad or truncate to exact feature size
            for i, features in enumerate(sentiment_features):
                if len(features) > self.config.sentiment_node_features:
                    sentiment_features[i] = features[:self.config.sentiment_node_features]
                elif len(features) < self.config.sentiment_node_features:
                    sentiment_features[i] = features + [0.0] * (self.config.sentiment_node_features - len(features))

            return torch.FloatTensor(sentiment_features)

        except Exception as e:
            logger.error(f"Error creating sentiment nodes: {e}")
            # Return default sentiment nodes
            default_features = [[0.0] * self.config.sentiment_node_features for _ in range(self.num_sentiment_nodes)]
            return torch.FloatTensor(default_features)

    def create_onchain_nodes(self, market_data: Dict[str, Any]) -> torch.Tensor:
        """Create on-chain data nodes for the graph (Etherscan and Glassnode integration)"""
        try:
            onchain_features = []

            # Extract on-chain data from market data
            live_aggregated = market_data.get('live_aggregated', {})

            # Node 1: Ethereum network activity
            eth_data = live_aggregated.get('ethereum_data', {})
            eth_features = [
                eth_data.get('gas_price', 20.0) / 100.0,  # Normalized gas price
                eth_data.get('network_utilization', 0.5),  # Network utilization
                eth_data.get('active_addresses', 500000) / 1000000.0,  # Normalized active addresses
                eth_data.get('transaction_count', 1000000) / 2000000.0,  # Normalized tx count
                eth_data.get('defi_tvl_change', 0.0),  # DeFi TVL change
                eth_data.get('whale_activity', 0.0),  # Large transaction activity
            ]

            # Node 2: Bitcoin network metrics
            btc_data = live_aggregated.get('bitcoin_data', {})
            btc_features = [
                btc_data.get('hash_rate', 300e18) / 400e18,  # Normalized hash rate
                btc_data.get('difficulty_adjustment', 0.0),  # Difficulty change
                btc_data.get('mempool_size', 50) / 100.0,  # Normalized mempool
                btc_data.get('active_addresses', 800000) / 1000000.0,  # Normalized addresses
                btc_data.get('hodl_waves', 0.6),  # Long-term holder ratio
                btc_data.get('exchange_flows', 0.0),  # Exchange inflow/outflow
            ]

            # Node 3: Cross-chain bridge activity
            bridge_data = live_aggregated.get('bridge_data', {})
            bridge_features = [
                bridge_data.get('total_volume', 0.0) / 1e9,  # Normalized bridge volume
                bridge_data.get('eth_to_l2', 0.0),  # ETH to L2 flows
                bridge_data.get('btc_wrapped', 0.0),  # Wrapped BTC activity
                bridge_data.get('stablecoin_flows', 0.0),  # Stablecoin movements
                bridge_data.get('arbitrage_opportunities', 0.0),  # Cross-chain arbitrage
                bridge_data.get('bridge_security_score', 0.8),  # Security assessment
            ]

            # Node 4: DeFi protocol metrics
            defi_data = live_aggregated.get('defi_data', {})
            defi_features = [
                defi_data.get('total_value_locked', 50e9) / 100e9,  # Normalized TVL
                defi_data.get('yield_farming_apy', 0.05),  # Average yield
                defi_data.get('liquidation_risk', 0.1),  # Liquidation pressure
                defi_data.get('governance_activity', 0.0),  # DAO voting activity
                defi_data.get('new_protocol_launches', 0.0),  # Innovation metric
                defi_data.get('protocol_security_incidents', 0.0),  # Risk metric
            ]

            # Node 5: Stablecoin metrics
            stablecoin_data = live_aggregated.get('stablecoin_data', {})
            stablecoin_features = [
                stablecoin_data.get('total_supply', 100e9) / 200e9,  # Normalized supply
                stablecoin_data.get('depeg_risk', 0.0),  # Depeg risk score
                stablecoin_data.get('redemption_pressure', 0.0),  # Redemption activity
                stablecoin_data.get('yield_opportunities', 0.03),  # Stablecoin yields
                stablecoin_data.get('regulatory_risk', 0.1),  # Regulatory pressure
                stablecoin_data.get('adoption_rate', 0.0),  # New adoption metrics
            ]

            # Node 6: NFT and gaming metrics
            nft_data = live_aggregated.get('nft_data', {})
            nft_features = [
                nft_data.get('trading_volume', 0.0) / 1e6,  # Normalized NFT volume
                nft_data.get('floor_price_index', 0.0),  # Floor price trends
                nft_data.get('new_collections', 0.0),  # New collection launches
                nft_data.get('gaming_activity', 0.0),  # Gaming token activity
                nft_data.get('metaverse_land_sales', 0.0),  # Virtual real estate
                nft_data.get('creator_royalties', 0.0),  # Creator economy health
            ]

            # Node 7: Institutional adoption metrics
            institutional_data = live_aggregated.get('institutional_data', {})
            institutional_features = [
                institutional_data.get('etf_flows', 0.0),  # ETF inflows/outflows
                institutional_data.get('corporate_treasury', 0.0),  # Corporate adoption
                institutional_data.get('custody_growth', 0.0),  # Custody service growth
                institutional_data.get('derivatives_oi', 0.0),  # Open interest growth
                institutional_data.get('regulatory_clarity', 0.5),  # Regulatory environment
                institutional_data.get('institutional_sentiment', 0.0),  # Institutional surveys
            ]

            # Node 8: Macro on-chain correlations
            macro_onchain_data = live_aggregated.get('macro_onchain_data', {})
            macro_features = [
                macro_onchain_data.get('stock_crypto_correlation', 0.5),  # Stock market correlation
                macro_onchain_data.get('gold_crypto_correlation', 0.0),  # Gold correlation
                macro_onchain_data.get('dollar_strength_impact', 0.0),  # USD impact
                macro_onchain_data.get('inflation_hedge_demand', 0.0),  # Inflation hedge flows
                macro_onchain_data.get('geopolitical_safe_haven', 0.0),  # Safe haven demand
                macro_onchain_data.get('central_bank_policy_impact', 0.0),  # Monetary policy impact
            ]

            # Combine all on-chain features
            onchain_features = [
                eth_features, btc_features, bridge_features, defi_features,
                stablecoin_features, nft_features, institutional_features, macro_features
            ]

            # Pad or truncate to exact feature size
            for i, features in enumerate(onchain_features):
                if len(features) > self.config.onchain_node_features:
                    onchain_features[i] = features[:self.config.onchain_node_features]
                elif len(features) < self.config.onchain_node_features:
                    onchain_features[i] = features + [0.0] * (self.config.onchain_node_features - len(features))

            return torch.FloatTensor(onchain_features)

        except Exception as e:
            logger.error(f"Error creating on-chain nodes: {e}")
            # Return default on-chain nodes
            default_features = [[0.0] * self.config.onchain_node_features for _ in range(self.num_onchain_nodes)]
            return torch.FloatTensor(default_features)
