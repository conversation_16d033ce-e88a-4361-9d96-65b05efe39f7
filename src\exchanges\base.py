# src/exchanges/base.py
from abc import ABC, abstractmethod
from src.vault_client import VaultClient

class QuantumTrader(ABC):
    def __init__(self, exchange_name: str):
        vault = VaultClient()
        self.credentials = vault.get_exchange_secret(exchange_name)
        self.quantum_secured = True
        
    @abstractmethod
    async def execute_order(self, order: dict):
        """Quantum-resistant order execution"""
        raise NotImplementedError("Subclasses must implement execute_order")

    @abstractmethod
    async def get_balance(self) -> dict:
        """Get quantum-secured balance"""
        raise NotImplementedError("Subclasses must implement get_balance")