#!/usr/bin/env python3
"""
System Test Script - Diagnose and Fix Issues
"""

import sys
import os
import traceback
from pathlib import Path

def test_environment():
    """Test basic environment setup"""
    print("=== ENVIRONMENT TEST ===")
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Python path: {sys.path[:3]}...")

    # Clean Python path to remove conflicting paths
    cleaned_paths = []
    for path in sys.path:
        if 'E:\\$Root\\pip' not in path:
            cleaned_paths.append(path)
    sys.path = cleaned_paths
    print("✅ Python path cleaned")

    # Check conda environment
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'None')
    print(f"Conda environment: {conda_env}")

    return True

def test_basic_imports():
    """Test basic package imports"""
    print("\n=== BASIC IMPORTS TEST ===")
    
    packages = [
        'os', 'sys', 'pathlib', 'traceback',
        'numpy', 'torch', 'stable_baselines3', 
        'ccxt', 'cryptography', 'dotenv'
    ]
    
    results = {}
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package}")
            results[package] = True
        except ImportError as e:
            print(f"❌ {package}: {e}")
            results[package] = False
        except Exception as e:
            print(f"⚠️ {package}: {e}")
            results[package] = False
    
    return results

def test_credential_system():
    """Test credential decryption system"""
    print("\n=== CREDENTIAL SYSTEM TEST ===")
    
    try:
        # Add src to path
        sys.path.insert(0, 'src')
        
        # Test HybridCrypto import
        from src.utils.cryptpography.hybrid import HybridCrypto
        print("✅ HybridCrypto imported")
        
        # Test initialization
        crypto = HybridCrypto('src/utils/cryptography/private.pem')
        print("✅ HybridCrypto initialized")
        
        # Test environment loading
        from dotenv import load_dotenv
        load_dotenv('.env')
        print("✅ Environment loaded")
        
        # Test credential access
        bybit_key = os.getenv('BYBIT_API_KEY')
        encrypted_coinbase = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        
        print(f"Bybit API Key: {bybit_key}")
        print(f"Encrypted Coinbase exists: {bool(encrypted_coinbase)}")
        
        if encrypted_coinbase:
            try:
                decrypted = crypto.decrypt_value(encrypted_coinbase)
                print(f"✅ Coinbase decryption successful: {decrypted[:50]}...")
            except Exception as e:
                print(f"❌ Coinbase decryption failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Credential system failed: {e}")
        traceback.print_exc()
        return False

def test_neural_imports():
    """Test neural component imports"""
    print("\n=== NEURAL IMPORTS TEST ===")
    
    try:
        sys.path.insert(0, 'src')
        
        neural_components = [
            'src.neural.rl_agent.RLAgentManager',
            'src.neural.lstm_processor.LSTMProcessor',
            'src.neural.hybrid_agent.HybridTradingAgent',
            'src.neural.predictors.QuantumSecureTradingSystem'
        ]
        
        results = {}
        for component in neural_components:
            try:
                module_path, class_name = component.rsplit('.', 1)
                module = __import__(module_path, fromlist=[class_name])
                cls = getattr(module, class_name)
                print(f"✅ {component}")
                results[component] = True
            except Exception as e:
                print(f"❌ {component}: {e}")
                results[component] = False
        
        return results
        
    except Exception as e:
        print(f"❌ Neural imports failed: {e}")
        traceback.print_exc()
        return False

def test_exchange_connectivity():
    """Test exchange API connectivity"""
    print("\n=== EXCHANGE CONNECTIVITY TEST ===")
    
    try:
        sys.path.insert(0, 'src')
        
        # Test Bybit connection
        from src.exchanges.bybit_client import BybitClient
        
        bybit_key = os.getenv('BYBIT_API_KEY')
        bybit_secret = os.getenv('BYBIT_API_SECRET')
        
        if bybit_key and bybit_secret:
            bybit_client = BybitClient(
                api_key=bybit_key,
                api_secret=bybit_secret,
                testnet=False
            )
            print("✅ Bybit client initialized")
            
            # Test basic API call
            try:
                balance = bybit_client.get_balance()
                print(f"✅ Bybit balance retrieved: {balance}")
            except Exception as e:
                print(f"⚠️ Bybit balance call failed: {e}")
        else:
            print("❌ Bybit credentials not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Exchange connectivity failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run comprehensive system test"""
    print("🚀 COMPREHENSIVE SYSTEM TEST STARTING...")
    print("=" * 60)
    
    results = {}
    
    # Run all tests
    results['environment'] = test_environment()
    results['basic_imports'] = test_basic_imports()
    results['credentials'] = test_credential_system()
    results['neural'] = test_neural_imports()
    results['exchanges'] = test_exchange_connectivity()
    
    # Summary
    print("\n" + "=" * 60)
    print("🔍 TEST SUMMARY:")
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.upper()}: {status}")
    
    # Overall status
    all_passed = all(results.values())
    overall_status = "✅ ALL TESTS PASSED" if all_passed else "❌ SOME TESTS FAILED"
    print(f"\nOVERALL: {overall_status}")
    
    if all_passed:
        print("\n🎯 System is ready for live trading!")
    else:
        print("\n⚠️ System needs fixes before live trading can proceed.")
    
    return all_passed

if __name__ == "__main__":
    main()
