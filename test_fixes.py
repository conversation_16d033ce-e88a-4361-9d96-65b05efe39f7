#!/usr/bin/env python3
"""
Test All Fixes - Verify that all critical issues are resolved
"""

import os
import sys
import asyncio
from pathlib import Path
from decimal import Decimal

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Force Bybit-only environment
os.environ["BYBIT_ONLY_MODE"] = "true"
os.environ["COINBASE_ENABLED"] = "false"
os.environ["LIVE_TRADING"] = "true"
os.environ["REAL_MONEY_TRADING"] = "true"

async def test_all_fixes():
    """Test all critical fixes"""
    print("🧪 [TEST] Testing all critical fixes...")
    
    try:
        # Test 1: Import and initialize Bybit client
        print("\n🧪 [TEST-1] Testing Bybit client initialization...")
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            print("❌ [TEST-1] Missing API credentials")
            return False
        
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        
        if bybit_client.session:
            print("✅ [TEST-1] Bybit client initialized successfully")
        else:
            print("❌ [TEST-1] Failed to initialize Bybit client")
            return False
        
        # Test 2: Test balance validation fix
        print("\n🧪 [TEST-2] Testing balance validation fix...")
        from src.trading.balance_aware_order_manager import BalanceAwareOrderManager
        
        balance_manager = BalanceAwareOrderManager(
            exchange_clients={'bybit': bybit_client},
            config={'balance_buffer': 0.05}
        )
        
        # Test with reasonable amount (should not cause massive calculation)
        test_amount = Decimal('10.0')  # $10 USDT
        balance_check = await balance_manager.validate_order_balance(
            symbol="BTCUSDT",
            side="buy",
            amount=test_amount,
            exchange="bybit"
        )
        
        if balance_check.required_balance == test_amount:
            print("✅ [TEST-2] Balance validation fix working - no double conversion")
        else:
            print(f"❌ [TEST-2] Balance validation issue - required: {balance_check.required_balance}, expected: {test_amount}")
            return False
        
        # Test 3: Test precision requirements
        print("\n🧪 [TEST-3] Testing precision requirements...")
        
        # Test SOLUSDT precision (should be 3 decimals)
        sol_requirements = bybit_client._get_bybit_minimum_requirements('SOLUSDT')
        if sol_requirements['qty_precision'] <= 3:
            print(f"✅ [TEST-3] SOLUSDT precision is safe: {sol_requirements['qty_precision']} decimals")
        else:
            print(f"❌ [TEST-3] SOLUSDT precision too high: {sol_requirements['qty_precision']} decimals")
            return False
        
        # Test 4: Test invalid symbol blacklist
        print("\n🧪 [TEST-4] Testing invalid symbol blacklist...")
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        
        trading_engine = MultiCurrencyTradingEngine({'bybit': bybit_client})
        
        if 'MATICUSDT' in trading_engine.invalid_symbols_blacklist:
            print("✅ [TEST-4] MATICUSDT is properly blacklisted")
        else:
            print("❌ [TEST-4] MATICUSDT not in blacklist")
            return False
        
        if 'EOSUSDT' in trading_engine.invalid_symbols_blacklist:
            print("✅ [TEST-4] EOSUSDT is properly blacklisted")
        else:
            print("❌ [TEST-4] EOSUSDT not in blacklist")
            return False
        
        # Test 5: Test order amount validation
        print("\n🧪 [TEST-5] Testing order amount validation...")
        
        # Test with massive amount (should be rejected)
        massive_amount = Decimal('1000000')  # 1 million
        massive_balance_check = await balance_manager.validate_order_balance(
            symbol="BTCUSDT",
            side="buy",
            amount=massive_amount,
            exchange="bybit"
        )
        
        if massive_balance_check.status.value == "insufficient":
            print("✅ [TEST-5] Massive order amounts are properly rejected")
        else:
            print("❌ [TEST-5] Massive order amounts not rejected")
            return False
        
        print("\n🎉 [SUCCESS] All critical fixes are working correctly!")
        print("✅ No more massive order amounts")
        print("✅ No more precision errors")
        print("✅ No more invalid symbols")
        print("✅ Proper balance validation")
        print("✅ Order amount safety checks")
        
        return True
        
    except Exception as e:
        print(f"❌ [TEST] Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 [TEST-FIXES] Testing all critical fixes...")
    
    try:
        success = asyncio.run(test_all_fixes())
        if success:
            print("\n✅ [RESULT] All fixes verified successfully!")
            print("🚀 [RESULT] System ready for real trading!")
            sys.exit(0)
        else:
            print("\n❌ [RESULT] Some fixes need attention")
            sys.exit(1)
    except Exception as e:
        print(f"❌ [TEST-FIXES] Testing failed: {e}")
        sys.exit(1)
