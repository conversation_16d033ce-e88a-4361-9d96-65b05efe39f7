#!/usr/bin/env python3
"""
HIGH-SPEED CONNECTION POOL MANAGER
Implements aggressive connection pooling and WebSocket management for speed
"""

import asyncio
import aiohttp
import websockets
import json
import time
import logging
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
import ssl
import certifi

from ..performance.speed_optimizer import fast_api_call, cached_market_data, speed_optimizer

logger = logging.getLogger(__name__)

@dataclass
class ConnectionStats:
    """Connection statistics"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    last_used: datetime = None
    connection_errors: int = 0

class HighSpeedConnectionPool:
    """High-speed connection pool with aggressive optimization"""
    
    def __init__(self, max_connections: int = 20, timeout: float = 0.3):
        self.max_connections = max_connections
        self.timeout = timeout
        self.session = None
        self.websocket_connections = {}
        self.connection_stats = {}
        self.rate_limiters = {}
        
        # SSL context for speed
        self.ssl_context = ssl.create_default_context(cafile=certifi.where())
        self.ssl_context.check_hostname = False
        self.ssl_context.verify_mode = ssl.CERT_NONE  # For speed (use with caution)
        
        # Connection pool settings
        self.connector_config = {
            'limit': max_connections,
            'limit_per_host': max_connections // 2,
            'ttl_dns_cache': 300,  # 5 minutes DNS cache
            'use_dns_cache': True,
            'keepalive_timeout': 30,
            'enable_cleanup_closed': True,
            'force_close': False,
            'ssl': self.ssl_context
        }
        
        # Initialize session (will be done lazily when needed)
        self._session_initialized = False
    
    async def _initialize_session(self):
        """Initialize HTTP session with optimized settings"""
        if self._session_initialized:
            return

        connector = aiohttp.TCPConnector(**self.connector_config)

        timeout = aiohttp.ClientTimeout(
            total=self.timeout,
            connect=self.timeout / 2,
            sock_read=self.timeout / 2
        )

        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': 'AutoGPT-Trader/1.0',
                'Connection': 'keep-alive',
                'Accept-Encoding': 'gzip, deflate'
            }
        )

        self._session_initialized = True
        logger.info(f"✅ [POOL] High-speed connection pool initialized (max: {self.max_connections})")
    
    @fast_api_call
    async def make_request(self, method: str, url: str, headers: Optional[Dict] = None, 
                          data: Optional[Dict] = None, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make high-speed HTTP request"""
        if not self.session:
            await self._initialize_session()
        
        start_time = time.time()
        endpoint = url.split('/')[-1] if '/' in url else url
        
        try:
            # Update stats
            if endpoint not in self.connection_stats:
                self.connection_stats[endpoint] = ConnectionStats()
            
            stats = self.connection_stats[endpoint]
            stats.total_requests += 1
            stats.last_used = datetime.now()
            
            # Make request with circuit breaker
            circuit_breaker = speed_optimizer.get_circuit_breaker(f"api_{endpoint}")
            
            async def _make_request():
                async with self.session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=f"HTTP {response.status}"
                        )
            
            result = circuit_breaker.call(_make_request)
            if asyncio.iscoroutine(result):
                result = await result
            
            # Update success stats
            response_time = (time.time() - start_time) * 1000
            stats.successful_requests += 1
            stats.avg_response_time = (
                (stats.avg_response_time * (stats.successful_requests - 1) + response_time) /
                stats.successful_requests
            )
            
            return result
            
        except Exception as e:
            # Update failure stats
            stats.failed_requests += 1
            stats.connection_errors += 1
            
            logger.warning(f"⚠️ [POOL] Request failed: {method} {url} - {e}")
            raise
    
    async def create_websocket_connection(self, url: str, on_message: Callable, 
                                        exchange_name: str) -> bool:
        """Create high-speed WebSocket connection"""
        try:
            # Close existing connection if any
            if exchange_name in self.websocket_connections:
                await self._close_websocket(exchange_name)
            
            # Create new connection with speed optimizations
            websocket = await websockets.connect(
                url,
                ssl=self.ssl_context,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=5,
                max_size=2**20,  # 1MB max message size
                compression=None  # Disable compression for speed
            )
            
            self.websocket_connections[exchange_name] = {
                'websocket': websocket,
                'url': url,
                'on_message': on_message,
                'connected_at': datetime.now(),
                'message_count': 0,
                'last_message': None
            }
            
            # Start message handler
            asyncio.create_task(self._handle_websocket_messages(exchange_name))
            
            logger.info(f"✅ [POOL] WebSocket connected: {exchange_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [POOL] WebSocket connection failed: {exchange_name} - {e}")
            return False
    
    async def _handle_websocket_messages(self, exchange_name: str):
        """Handle WebSocket messages with speed optimization"""
        connection_info = self.websocket_connections.get(exchange_name)
        if not connection_info:
            return
        
        websocket = connection_info['websocket']
        on_message = connection_info['on_message']
        
        try:
            async for message in websocket:
                try:
                    # Parse message quickly
                    if isinstance(message, str):
                        data = json.loads(message)
                    else:
                        data = message
                    
                    # Update stats
                    connection_info['message_count'] += 1
                    connection_info['last_message'] = datetime.now()
                    
                    # Handle message asynchronously for speed
                    asyncio.create_task(on_message(data))
                    
                except json.JSONDecodeError:
                    logger.warning(f"⚠️ [POOL] Invalid JSON from {exchange_name}")
                except Exception as e:
                    logger.error(f"❌ [POOL] Message handling error: {exchange_name} - {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.warning(f"⚠️ [POOL] WebSocket disconnected: {exchange_name}")
        except Exception as e:
            logger.error(f"❌ [POOL] WebSocket error: {exchange_name} - {e}")
        finally:
            # Clean up connection
            if exchange_name in self.websocket_connections:
                del self.websocket_connections[exchange_name]
    
    async def _close_websocket(self, exchange_name: str):
        """Close WebSocket connection"""
        if exchange_name in self.websocket_connections:
            try:
                websocket = self.websocket_connections[exchange_name]['websocket']
                await websocket.close()
            except Exception as e:
                logger.warning(f"⚠️ [POOL] Error closing WebSocket: {exchange_name} - {e}")
            finally:
                del self.websocket_connections[exchange_name]
    
    async def send_websocket_message(self, exchange_name: str, message: Dict[str, Any]) -> bool:
        """Send message via WebSocket"""
        if exchange_name not in self.websocket_connections:
            logger.warning(f"⚠️ [POOL] No WebSocket connection: {exchange_name}")
            return False
        
        try:
            websocket = self.websocket_connections[exchange_name]['websocket']
            await websocket.send(json.dumps(message))
            return True
        except Exception as e:
            logger.error(f"❌ [POOL] WebSocket send error: {exchange_name} - {e}")
            return False
    
    def get_connection_health(self) -> Dict[str, Any]:
        """Get connection pool health status"""
        total_requests = sum(stats.total_requests for stats in self.connection_stats.values())
        total_successful = sum(stats.successful_requests for stats in self.connection_stats.values())
        total_failed = sum(stats.failed_requests for stats in self.connection_stats.values())
        
        success_rate = (total_successful / total_requests * 100) if total_requests > 0 else 0
        
        # WebSocket status
        websocket_status = {}
        for name, info in self.websocket_connections.items():
            websocket_status[name] = {
                'connected': True,
                'uptime_seconds': (datetime.now() - info['connected_at']).total_seconds(),
                'message_count': info['message_count'],
                'last_message_ago': (
                    (datetime.now() - info['last_message']).total_seconds()
                    if info['last_message'] else None
                )
            }
        
        # Endpoint performance
        endpoint_performance = {}
        for endpoint, stats in self.connection_stats.items():
            endpoint_performance[endpoint] = {
                'avg_response_time_ms': stats.avg_response_time,
                'success_rate': (stats.successful_requests / stats.total_requests * 100) 
                               if stats.total_requests > 0 else 0,
                'total_requests': stats.total_requests,
                'connection_errors': stats.connection_errors
            }
        
        return {
            'pool_status': 'healthy' if success_rate > 90 else 'degraded' if success_rate > 70 else 'critical',
            'total_requests': total_requests,
            'success_rate': success_rate,
            'active_websockets': len(self.websocket_connections),
            'websocket_status': websocket_status,
            'endpoint_performance': endpoint_performance,
            'session_active': self.session is not None and not self.session.closed
        }
    
    async def health_check(self) -> bool:
        """Perform health check on connection pool"""
        try:
            # Check HTTP session
            if not self.session or self.session.closed:
                await self._initialize_session()
            
            # Check WebSocket connections
            disconnected_connections = []
            for name, info in self.websocket_connections.items():
                websocket = info['websocket']
                if websocket.closed:
                    disconnected_connections.append(name)
            
            # Remove disconnected WebSockets
            for name in disconnected_connections:
                del self.websocket_connections[name]
                logger.warning(f"⚠️ [POOL] Removed disconnected WebSocket: {name}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [POOL] Health check failed: {e}")
            return False
    
    async def optimize_connections(self):
        """Optimize connection pool performance"""
        try:
            # Close idle connections
            current_time = datetime.now()
            idle_threshold = timedelta(minutes=5)
            
            idle_endpoints = [
                endpoint for endpoint, stats in self.connection_stats.items()
                if stats.last_used and (current_time - stats.last_used) > idle_threshold
            ]
            
            for endpoint in idle_endpoints:
                # Reset stats for idle endpoints
                self.connection_stats[endpoint] = ConnectionStats()
            
            # Reconnect failed WebSockets
            for name, info in list(self.websocket_connections.items()):
                websocket = info['websocket']
                if websocket.closed:
                    logger.info(f"🔄 [POOL] Reconnecting WebSocket: {name}")
                    await self.create_websocket_connection(
                        info['url'], info['on_message'], name
                    )
            
            logger.debug("🔧 [POOL] Connection optimization completed")
            
        except Exception as e:
            logger.error(f"❌ [POOL] Connection optimization failed: {e}")
    
    async def shutdown(self):
        """Shutdown connection pool"""
        try:
            # Close all WebSocket connections
            for name in list(self.websocket_connections.keys()):
                await self._close_websocket(name)
            
            # Close HTTP session
            if self.session and not self.session.closed:
                await self.session.close()
            
            logger.info("✅ [POOL] Connection pool shutdown completed")
            
        except Exception as e:
            logger.error(f"❌ [POOL] Shutdown error: {e}")

# Global connection pool instance
connection_pool = HighSpeedConnectionPool()

# Convenience functions
async def fast_http_request(method: str, url: str, **kwargs) -> Dict[str, Any]:
    """Make fast HTTP request using connection pool"""
    return await connection_pool.make_request(method, url, **kwargs)

async def create_fast_websocket(url: str, on_message: Callable, exchange_name: str) -> bool:
    """Create fast WebSocket connection"""
    return await connection_pool.create_websocket_connection(url, on_message, exchange_name)

async def send_websocket_message(exchange_name: str, message: Dict[str, Any]) -> bool:
    """Send WebSocket message"""
    return await connection_pool.send_websocket_message(exchange_name, message)
