from .base import BaseStrategy, TradeSignal
import numpy as np

# Optional pandas import with fallback
try:
    import pandas as pd
except ImportError:
    # Create mock pandas for fallback
    class MockPandas:
        def DataFrame(self, data=None):
            return data or {}
    pd = MockPandas()
import os
import logging
from datetime import datetime
from typing import Dict, List

logger = logging.getLogger("live.trading")

class MeanReversionStrategy(BaseStrategy):
    def __init__(self, config: Dict):
        super().__init__(config)
        self.window = config.get("window", 20)
        self.z_threshold = config.get("z_threshold", 2.0)
        self.position_size = config.get("position_size", 0.05)
        self.circuit_breaker = False

    async def analyze_market(self, market_data: Dict) -> List[TradeSignal]:
        if os.getenv("TRADING_MODE") != "live":
            raise EnvironmentError("Strategy called in non-live mode")
            
        try:
            signals = []
            for symbol, data in market_data.items():
                df = pd.DataFrame(data)
                df['returns'] = np.log(df['price'] / df['price'].shift(1))
                
                # Live volatility check
                rolling_vol = df['returns'].rolling(self.window).std() * np.sqrt(365)
                if rolling_vol[-1] > 1.0:  # 100% annualized volatility threshold
                    self._trigger_circuit_breaker()
                    return []

                signals += self._generate_signals(df, symbol)
                
            return self.apply_risk_management(signals)
            
        except Exception as e:
            logger.error(f"MeanReversion error: {str(e)}")
            self._trigger_circuit_breaker()
            return []

    def _generate_signals(self, df: pd.DataFrame, symbol: str) -> List[TradeSignal]:
        df['z_score'] = (df['price'] - df['price'].rolling(self.window).mean()) / df['price'].rolling(self.window).std()
        
        signals = []
        last_row = df.iloc[-1]
        
        if last_row['z_score'] < -self.z_threshold:
            signals.append(TradeSignal(
                symbol=symbol,
                action="buy",
                confidence=min(abs(last_row['z_score'])/self.z_threshold, 0.95),
                amount_percent=self.position_size,
                timestamp=datetime.utcnow().isoformat()
            ))
        elif last_row['z_score'] > self.z_threshold:
            signals.append(TradeSignal(
                symbol=symbol,
                action="sell",
                confidence=min(abs(last_row['z_score'])/self.z_threshold, 0.95),
                amount_percent=self.position_size,
                timestamp=datetime.utcnow().isoformat()
            ))
        return signals

    def _trigger_circuit_breaker(self):
        self.circuit_breaker = True
        logger.critical("Circuit breaker triggered - halting trading")

from jsonschema import Draft7Validator

class ConfigManager:
    def __init__(self):
        self.validator = Draft7Validator(self._load_schema())
        
    def validate(self, config: dict) -> list:
        """Return list of errors instead of raising"""
        return list(self.validator.iter_errors(config))