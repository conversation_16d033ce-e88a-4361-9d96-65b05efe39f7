#!/usr/bin/env python3
"""
Test numpy import fix
"""

import sys
import os

# Clean Python path to remove any conflicting paths
print("Original Python path:")
for i, path in enumerate(sys.path):
    print(f"  {i}: {path}")

# Remove any paths that might contain numpy source
cleaned_paths = []
for path in sys.path:
    # Skip paths that might contain numpy source
    if 'numpy' not in path.lower() and 'E:\\$Root\\pip' not in path:
        cleaned_paths.append(path)

sys.path = cleaned_paths

print("\nCleaned Python path:")
for i, path in enumerate(sys.path):
    print(f"  {i}: {path}")

# Now try to import numpy
print("\nTesting numpy import...")
try:
    import numpy as np
    print(f"✅ Numpy imported successfully: {np.__version__}")
    print(f"Numpy location: {np.__file__}")
except Exception as e:
    print(f"❌ Numpy import failed: {e}")

# Test torch
print("\nTesting torch import...")
try:
    import torch
    print(f"✅ Torch imported successfully: {torch.__version__}")
except Exception as e:
    print(f"❌ Torch import failed: {e}")

# Test pandas
print("\nTesting pandas import...")
try:
    import pandas as pd
    print(f"✅ Pandas imported successfully: {pd.__version__}")
except Exception as e:
    print(f"❌ Pandas import failed: {e}")

print("\nTest completed!")
