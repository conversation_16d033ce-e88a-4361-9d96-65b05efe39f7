#!/usr/bin/env python3
"""
Live Data Fetcher System
Advanced multi-provider data aggregation with error handling, outlier detection, and real-time serving
Integrates with AutoGPT Trader for enhanced market data analysis
"""

import asyncio
import aiohttp
import logging
import time
import os
import json
import traceback
import numpy as np
from typing import List, Dict, Any, Optional
from aiohttp import web
from decimal import Decimal
from datetime import datetime, timezone

# Configure logging
logger = logging.getLogger(__name__)

# ---------------------- Utilities ----------------------

def remove_outliers(values: List[float], z_threshold: float = 2.5) -> List[float]:
    """Remove statistical outliers using Z-score method"""
    if not values or len(values) < 2:
        return values
    
    arr = np.array(values)
    if arr.size == 0:
        return []
    
    # Handle case where std is 0 (all values are the same)
    std = arr.std()
    if std == 0:
        return arr.tolist()
    
    z = np.abs((arr - arr.mean()) / std)
    return arr[z < z_threshold].tolist()

def calculate_price_momentum(current: float, previous: float) -> float:
    """Calculate price momentum as percentage change"""
    if previous == 0:
        return 0.0
    return ((current - previous) / previous) * 100

def calculate_volatility(prices: List[float]) -> float:
    """Calculate price volatility using standard deviation"""
    if len(prices) < 2:
        return 0.0
    return float(np.std(prices))

# ---------------------- Error Solver ----------------------
class ErrorSolver:
    """Intelligent error resolution system"""
    def __init__(self):
        self.adjustments: Dict[str, Any] = {}
        self.error_history: Dict[str, List[str]] = {}

    async def solve(self, prov_name: str, exc: Exception) -> bool:
        """Attempt to solve common API errors automatically"""
        msg = str(exc).lower()
        
        # Track error history
        if prov_name not in self.error_history:
            self.error_history[prov_name] = []
        self.error_history[prov_name].append(msg)
        
        # Keep only last 10 errors
        self.error_history[prov_name] = self.error_history[prov_name][-10:]
        
        # Timeout errors
        if 'timeout' in msg or 'timeouterror' in msg:
            current_timeout = self.adjustments.get(prov_name, {}).get('timeout', 10)
            new_timeout = min(current_timeout * 1.5, 60)  # Cap at 60 seconds
            self.adjustments[prov_name] = {'timeout': new_timeout}
            logger.info(f"🔧 [{prov_name}] Increased timeout to {new_timeout}s")
            return True
        
        # Rate limiting
        if '429' in msg or 'rate limit' in msg or 'too many requests' in msg:
            current_backoff = self.adjustments.get(prov_name, {}).get('backoff', 1)
            new_backoff = min(current_backoff * 2, 30)  # Cap at 30 seconds
            self.adjustments[prov_name] = {'backoff': new_backoff}
            logger.info(f"🔧 [{prov_name}] Increased backoff to {new_backoff}s")
            return True
        
        # Connection errors
        if 'connection' in msg or 'network' in msg:
            self.adjustments[prov_name] = {'retry_connection': True}
            logger.info(f"🔧 [{prov_name}] Enabled connection retry")
            return True
        
        # Invalid response format
        if 'json' in msg or 'decode' in msg:
            self.adjustments[prov_name] = {'validate_response': True}
            logger.info(f"🔧 [{prov_name}] Enabled response validation")
            return True
        
        return False

    def get_error_stats(self, prov_name: str) -> Dict[str, Any]:
        """Get error statistics for a provider"""
        errors = self.error_history.get(prov_name, [])
        if not errors:
            return {'total_errors': 0, 'recent_errors': 0}
        
        return {
            'total_errors': len(errors),
            'recent_errors': len([e for e in errors[-5:]]),  # Last 5 errors
            'most_common': max(set(errors), key=errors.count) if errors else None
        }

# ---------------------- Retry Decorator ----------------------

def retry(max_attempts: int = 3, base_backoff: float = 1.0):
    """Advanced retry decorator with exponential backoff and error solving"""
    def decorator(func):
        async def wrapper(self, session: aiohttp.ClientSession, *args, **kwargs):
            backoff = base_backoff
            last_exception = None
            
            for attempt in range(1, max_attempts + 1):
                try:
                    # Apply solver adjustments
                    adj = self.solver.adjustments.get(self.name, {})
                    if 'timeout' in adj:
                        kwargs['timeout'] = adj['timeout']
                    if 'backoff' in adj and attempt > 1:
                        await asyncio.sleep(adj['backoff'])
                    
                    result = await func(self, session, *args, **kwargs)
                    
                    # Reset adjustments on success
                    if self.name in self.solver.adjustments:
                        self.solver.adjustments[self.name] = {}
                    
                    return result
                    
                except Exception as e:
                    last_exception = e
                    logger.warning(f"⚠️ [{self.name}] Attempt {attempt}/{max_attempts} failed: {e}")
                    
                    # Try to solve the error
                    solved = await self.solver.solve(self.name, e)
                    if solved and attempt < max_attempts:
                        continue
                    
                    if attempt == max_attempts:
                        logger.error(f"❌ [{self.name}] Failed after {max_attempts} attempts")
                        break
                    
                    # Exponential backoff
                    await asyncio.sleep(backoff)
                    backoff *= 2
            
            # Return empty dict on failure but log the error
            logger.error(f"❌ [{self.name}] Provider failed completely: {last_exception}")
            return {}
        return wrapper
    return decorator

# ---------------------- Base Provider ----------------------
class Provider:
    """Base class for data providers"""
    def __init__(self, name: str, solver: ErrorSolver):
        self.name = name
        self.solver = solver
        self.last_fetch_time = 0
        self.fetch_count = 0
        self.success_count = 0
        self.cache = {}
        self.cache_ttl = 30  # 30 seconds cache

    async def fetch(self, session: aiohttp.ClientSession) -> Dict[str, float]:
        """Fetch data from provider - to be implemented by subclasses"""
        raise NotImplementedError

    def get_success_rate(self) -> float:
        """Calculate provider success rate"""
        if self.fetch_count == 0:
            return 0.0
        return (self.success_count / self.fetch_count) * 100

    def is_cache_valid(self) -> bool:
        """Check if cached data is still valid"""
        return (time.time() - self.last_fetch_time) < self.cache_ttl

    async def fetch_with_cache(self, session: aiohttp.ClientSession) -> Dict[str, float]:
        """Fetch data with caching support"""
        self.fetch_count += 1
        
        # Return cached data if valid
        if self.is_cache_valid() and self.cache:
            logger.debug(f"📋 [{self.name}] Using cached data")
            return self.cache
        
        try:
            data = await self.fetch(session)
            if data:
                self.success_count += 1
                self.cache = data
                self.last_fetch_time = time.time()
                logger.info(f"✅ [{self.name}] Fetched {len(data)} data points")
            return data
        except Exception as e:
            logger.error(f"❌ [{self.name}] Fetch failed: {e}")
            return {}

# ---------------------- CoinGecko Provider ----------------------
class CoinGeckoProvider(Provider):
    """CoinGecko API provider with enhanced features"""
    BASE_URL = "https://api.coingecko.com/api/v3"
    
    def __init__(self, ids: List[str], vs: List[str], solver: ErrorSolver):
        super().__init__('CoinGecko', solver)
        self.ids = ids
        self.vs = vs
        self.api_key = os.getenv('COINGECKO_API_KEY')  # Optional Pro API key

    @retry()
    async def fetch(self, session: aiohttp.ClientSession, **kwargs) -> Dict[str, float]:
        """Fetch price data from CoinGecko"""
        url = f"{self.BASE_URL}/simple/price"
        params = {
            "ids": ",".join(self.ids),
            "vs_currencies": ",".join(self.vs),
            "include_24hr_change": "true",
            "include_market_cap": "true"
        }
        
        headers = {}
        if self.api_key:
            headers['x-cg-pro-api-key'] = self.api_key
        
        timeout = kwargs.get('timeout', 10)
        async with session.get(url, params=params, headers=headers, timeout=timeout) as resp:
            resp.raise_for_status()
            data = await resp.json()
            
            result = {}
            for coin_id in self.ids:
                if coin_id in data:
                    coin_data = data[coin_id]
                    for vs_currency in self.vs:
                        if vs_currency in coin_data:
                            key = f"{coin_id}_{vs_currency}"
                            result[key] = float(coin_data[vs_currency])
                            
                            # Add additional metrics if available
                            if f"{vs_currency}_24h_change" in coin_data:
                                result[f"{key}_24h_change"] = float(coin_data[f"{vs_currency}_24h_change"])
                            if f"{vs_currency}_market_cap" in coin_data:
                                result[f"{key}_market_cap"] = float(coin_data[f"{vs_currency}_market_cap"])
            
            return result

# ---------------------- Binance Provider ----------------------
class BinanceProvider(Provider):
    """Binance API provider for real-time price data"""
    BASE_URL = "https://api.binance.com/api/v3"

    def __init__(self, symbols: List[str], solver: ErrorSolver):
        super().__init__('Binance', solver)
        self.symbols = symbols

    @retry()
    async def fetch(self, session: aiohttp.ClientSession, **kwargs) -> Dict[str, float]:
        """Fetch price data from Binance"""
        url = f"{self.BASE_URL}/ticker/price"
        timeout = kwargs.get('timeout', 10)

        result = {}

        # Fetch specific symbols
        for symbol in self.symbols:
            try:
                symbol_url = f"{url}?symbol={symbol.upper()}"
                async with session.get(symbol_url, timeout=timeout) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        result[f"binance_{symbol.lower()}"] = float(data['price'])
            except Exception as e:
                logger.warning(f"⚠️ [BINANCE] Failed to fetch {symbol}: {e}")
                continue

        return result

# ---------------------- Bybit Provider ----------------------
class BybitProvider(Provider):
    """Bybit API provider for real-time data"""
    BASE_URL = "https://api.bybit.com/v2/public"

    def __init__(self, symbols: List[str], solver: ErrorSolver):
        super().__init__('Bybit', solver)
        self.symbols = symbols

    @retry()
    async def fetch(self, session: aiohttp.ClientSession, **kwargs) -> Dict[str, float]:
        """Fetch price data from Bybit"""
        result = {}
        timeout = kwargs.get('timeout', 10)

        for symbol in self.symbols:
            try:
                url = f"{self.BASE_URL}/tickers?symbol={symbol}"
                async with session.get(url, timeout=timeout) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        if 'result' in data and data['result']:
                            ticker = data['result'][0]
                            result[f"bybit_{symbol.lower()}"] = float(ticker['last_price'])

                            # Add additional metrics
                            if 'price_24h_pcnt' in ticker:
                                result[f"bybit_{symbol.lower()}_24h_change"] = float(ticker['price_24h_pcnt']) * 100
            except Exception as e:
                logger.warning(f"⚠️ [BYBIT] Failed to fetch {symbol}: {e}")
                continue

        return result

# ---------------------- Etherscan Provider ----------------------
class EtherscanProvider(Provider):
    """Etherscan API provider for Ethereum address balance tracking"""
    BASE_URL = "https://api.etherscan.io/api"

    def __init__(self, addresses: List[str], solver: ErrorSolver):
        super().__init__('Etherscan', solver)
        self.api_key = os.getenv('ETHERSCAN_API_KEY')
        self.addresses = addresses

    @retry()
    async def fetch(self, session: aiohttp.ClientSession, **kwargs) -> Dict[str, float]:
        """Fetch Ethereum address balances"""
        if not self.api_key:
            logger.warning("⚠️ [ETHERSCAN] No API key provided, skipping")
            return {}

        result = {}
        timeout = kwargs.get('timeout', 10)

        for addr in self.addresses:
            try:
                params = {
                    'module': 'account',
                    'action': 'balance',
                    'address': addr,
                    'tag': 'latest',
                    'apikey': self.api_key
                }

                async with session.get(self.BASE_URL, params=params, timeout=timeout) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        if data.get('status') == '1':
                            balance_wei = int(data['result'])
                            balance_eth = balance_wei / 1e18
                            # Create a short address identifier
                            addr_short = f"{addr[:6]}...{addr[-4:]}"
                            result[f"etherscan_{addr_short}_balance_eth"] = balance_eth
                        else:
                            logger.warning(f"⚠️ [ETHERSCAN] API error for {addr}: {data.get('message', 'Unknown error')}")

                # Rate limiting - Etherscan allows 5 calls/second
                await asyncio.sleep(0.2)

            except Exception as e:
                logger.warning(f"⚠️ [ETHERSCAN] Failed to fetch balance for {addr}: {e}")
                continue

        return result

# ---------------------- Glassnode Provider ----------------------
class GlassnodeProvider(Provider):
    """Glassnode API provider for on-chain analytics and metrics"""
    BASE_URL = "https://api.glassnode.com/v1/metrics"

    def __init__(self, metrics: List[str], assets: List[str], solver: ErrorSolver):
        super().__init__('Glassnode', solver)
        self.api_key = os.getenv('GLASSNODE_API_KEY')
        self.metrics = metrics
        self.assets = assets

    @retry()
    async def fetch(self, session: aiohttp.ClientSession, **kwargs) -> Dict[str, float]:
        """Fetch Glassnode on-chain metrics"""
        if not self.api_key:
            logger.warning("⚠️ [GLASSNODE] No API key provided, skipping")
            return {}

        result = {}
        timeout = kwargs.get('timeout', 15)

        for asset in self.assets:
            for metric in self.metrics:
                try:
                    # Build metric URL
                    url = f"{self.BASE_URL}/{metric}"
                    params = {
                        'a': asset.upper(),
                        'api_key': self.api_key,
                        'f': 'JSON',
                        's': int(time.time() - 86400),  # Last 24 hours
                        'u': int(time.time())
                    }

                    async with session.get(url, params=params, timeout=timeout) as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            if data and len(data) > 0:
                                # Get the latest value
                                latest_value = data[-1]['v']
                                if latest_value is not None:
                                    result[f"glassnode_{asset.lower()}_{metric}"] = float(latest_value)
                        else:
                            logger.warning(f"⚠️ [GLASSNODE] HTTP {resp.status} for {asset} {metric}")

                    # Rate limiting - Glassnode has strict limits
                    await asyncio.sleep(0.5)

                except Exception as e:
                    logger.warning(f"⚠️ [GLASSNODE] Failed to fetch {asset} {metric}: {e}")
                    continue

        return result

# ---------------------- Fear & Greed Index Provider ----------------------
class FearGreedProvider(Provider):
    """Fear & Greed Index provider for market sentiment analysis"""
    BASE_URL = "https://api.alternative.me/fng/"

    def __init__(self, solver: ErrorSolver):
        super().__init__('FearGreed', solver)

    @retry()
    async def fetch(self, session: aiohttp.ClientSession, **kwargs) -> Dict[str, float]:
        """Fetch Fear & Greed Index"""
        timeout = kwargs.get('timeout', 10)

        try:
            async with session.get(self.BASE_URL, timeout=timeout) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    if 'data' in data and len(data['data']) > 0:
                        current_data = data['data'][0]
                        score = int(current_data['value'])
                        classification = current_data.get('value_classification', 'Unknown')

                        return {
                            'fear_greed_index': float(score),
                            'fear_greed_classification': hash(classification) % 100  # Convert to numeric
                        }
                else:
                    logger.warning(f"⚠️ [FEAR_GREED] HTTP {resp.status}")

        except Exception as e:
            logger.warning(f"⚠️ [FEAR_GREED] Failed to fetch: {e}")

        return {}

# ---------------------- Live Data Fetcher ----------------------
class LiveDataFetcher:
    """Main data fetcher orchestrating multiple providers"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.solver = ErrorSolver()
        self.last_payload: Dict[str, Any] = {}
        self.price_history: Dict[str, List[float]] = {}
        self.max_history = 100

        # Initialize providers based on configuration
        self.providers: List[Provider] = self._initialize_providers()

        # Web server for serving data
        self.app = None
        self.runner = None

        logger.info(f"🚀 [FETCHER] Initialized with {len(self.providers)} providers")

    def _initialize_providers(self) -> List[Provider]:
        """Initialize data providers based on configuration"""
        providers = []

        # CoinGecko provider
        coingecko_config = self.config.get('coingecko', {})
        if coingecko_config.get('enabled', True):
            ids = coingecko_config.get('ids', ['bitcoin', 'ethereum', 'solana', 'cardano'])
            vs = coingecko_config.get('vs_currencies', ['usd', 'eth'])
            providers.append(CoinGeckoProvider(ids, vs, self.solver))

        # Binance provider
        binance_config = self.config.get('binance', {})
        if binance_config.get('enabled', True):
            symbols = binance_config.get('symbols', ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT'])
            providers.append(BinanceProvider(symbols, self.solver))

        # Bybit provider
        bybit_config = self.config.get('bybit', {})
        if bybit_config.get('enabled', True):
            symbols = bybit_config.get('symbols', ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT'])
            providers.append(BybitProvider(symbols, self.solver))

        # Etherscan provider
        etherscan_config = self.config.get('etherscan', {})
        if etherscan_config.get('enabled', False):  # Disabled by default
            addresses = etherscan_config.get('addresses', [])
            if addresses:
                providers.append(EtherscanProvider(addresses, self.solver))

        # Glassnode provider
        glassnode_config = self.config.get('glassnode', {})
        if glassnode_config.get('enabled', False):  # Disabled by default
            metrics = glassnode_config.get('metrics', ['market-cap', 'nvt', 'mvrv'])
            assets = glassnode_config.get('assets', ['BTC', 'ETH'])
            providers.append(GlassnodeProvider(metrics, assets, self.solver))

        # Fear & Greed Index provider
        fear_greed_config = self.config.get('fear_greed', {})
        if fear_greed_config.get('enabled', True):  # Enabled by default
            providers.append(FearGreedProvider(self.solver))

        return providers

    async def gather_live_data(self) -> Dict[str, Any]:
        """Gather data from all providers and aggregate"""
        start_time = time.time()
        raw_data: Dict[str, List[float]] = {}
        provider_stats = {}
        errors: List[str] = []

        async with aiohttp.ClientSession() as session:
            # Fetch from all providers concurrently
            tasks = []
            for provider in self.providers:
                task = asyncio.create_task(provider.fetch_with_cache(session))
                tasks.append((provider, task))

            # Collect results
            for provider, task in tasks:
                try:
                    data = await task
                    if data:
                        for key, value in data.items():
                            raw_data.setdefault(key, []).append(value)

                        provider_stats[provider.name] = {
                            'success_rate': provider.get_success_rate(),
                            'data_points': len(data),
                            'last_fetch': provider.last_fetch_time
                        }
                    else:
                        errors.append(f"{provider.name}: no data returned")
                        provider_stats[provider.name] = {
                            'success_rate': provider.get_success_rate(),
                            'data_points': 0,
                            'error': 'no data'
                        }
                except Exception as e:
                    error_msg = f"{provider.name}: {str(e)}"
                    errors.append(error_msg)
                    logger.error(f"❌ [FETCHER] Provider error: {error_msg}")

        # Aggregate and clean data
        aggregated = {}
        statistics = {}

        for key, values in raw_data.items():
            if not values:
                continue

            # Remove outliers
            clean_values = remove_outliers(values)
            if not clean_values:
                continue

            # Calculate aggregated value (median for robustness)
            aggregated_value = float(np.median(clean_values))
            aggregated[key] = aggregated_value

            # Calculate statistics
            statistics[key] = {
                'mean': float(np.mean(clean_values)),
                'median': aggregated_value,
                'std': float(np.std(clean_values)),
                'min': float(np.min(clean_values)),
                'max': float(np.max(clean_values)),
                'count': len(clean_values),
                'outliers_removed': len(values) - len(clean_values)
            }

            # Update price history for momentum calculation
            if key not in self.price_history:
                self.price_history[key] = []
            self.price_history[key].append(aggregated_value)

            # Keep only recent history
            if len(self.price_history[key]) > self.max_history:
                self.price_history[key] = self.price_history[key][-self.max_history:]

        # Calculate momentum and volatility
        momentum_data = {}
        volatility_data = {}

        for key, history in self.price_history.items():
            if len(history) >= 2:
                momentum_data[f"{key}_momentum"] = calculate_price_momentum(history[-1], history[-2])
            if len(history) >= 10:
                volatility_data[f"{key}_volatility"] = calculate_volatility(history[-10:])

        # Create comprehensive payload
        fetch_time = time.time() - start_time
        payload = {
            'timestamp': time.time(),
            'fetch_time_ms': round(fetch_time * 1000, 2),
            'live_aggregated': aggregated,
            'momentum': momentum_data,
            'volatility': volatility_data,
            'statistics': statistics,
            'provider_stats': provider_stats,
            'errors': errors,
            'data_quality': {
                'total_providers': len(self.providers),
                'successful_providers': len([p for p in provider_stats.values() if p.get('data_points', 0) > 0]),
                'total_data_points': sum(len(values) for values in raw_data.values()),
                'aggregated_symbols': len(aggregated)
            }
        }

        # Update last payload if we got any data
        if aggregated:
            self.last_payload = payload
            logger.info(f"📊 [FETCHER] Aggregated {len(aggregated)} symbols in {fetch_time:.2f}s")
        else:
            logger.warning("⚠️ [FETCHER] No data aggregated, retaining last payload")
            if not self.last_payload:
                self.last_payload = payload  # Store even empty payload if it's the first

        # Save snapshot
        await self._save_snapshot(payload)

        return self.last_payload

    async def _save_snapshot(self, payload: Dict[str, Any]):
        """Save data snapshot to file"""
        try:
            log_dir = self.config.get('log_dir', 'logs/data_snapshots')
            os.makedirs(log_dir, exist_ok=True)

            timestamp = int(time.time())
            filename = f"snapshot_{timestamp}.json"
            filepath = os.path.join(log_dir, filename)

            # Save with pretty formatting for readability
            with open(filepath, 'w') as f:
                json.dump(payload, f, indent=2, default=str)

            logger.debug(f"💾 [FETCHER] Snapshot saved: {filepath}")

            # Clean up old snapshots (keep last 100)
            await self._cleanup_old_snapshots(log_dir)

        except Exception as e:
            logger.error(f"❌ [FETCHER] Failed to save snapshot: {e}")

    async def _cleanup_old_snapshots(self, log_dir: str, keep_count: int = 100):
        """Clean up old snapshot files"""
        try:
            files = [f for f in os.listdir(log_dir) if f.startswith('snapshot_') and f.endswith('.json')]
            if len(files) <= keep_count:
                return

            # Sort by timestamp (extracted from filename)
            files.sort(key=lambda x: int(x.split('_')[1].split('.')[0]))

            # Remove oldest files
            files_to_remove = files[:-keep_count]
            for file in files_to_remove:
                os.remove(os.path.join(log_dir, file))

            logger.debug(f"🧹 [FETCHER] Cleaned up {len(files_to_remove)} old snapshots")

        except Exception as e:
            logger.error(f"❌ [FETCHER] Failed to cleanup snapshots: {e}")

    async def serve_web_api(self, host: str = '0.0.0.0', port: int = 8080):
        """Start web API server for serving live data"""
        async def handle_live_data(request):
            """Handle /live endpoint"""
            return web.json_response(self.last_payload)

        async def handle_provider_stats(request):
            """Handle /providers endpoint"""
            stats = {}
            for provider in self.providers:
                stats[provider.name] = {
                    'success_rate': provider.get_success_rate(),
                    'fetch_count': provider.fetch_count,
                    'success_count': provider.success_count,
                    'last_fetch': provider.last_fetch_time,
                    'cache_valid': provider.is_cache_valid(),
                    'error_stats': self.solver.get_error_stats(provider.name)
                }
            return web.json_response(stats)

        async def handle_health(request):
            """Handle /health endpoint"""
            health_data = {
                'status': 'healthy' if self.last_payload else 'no_data',
                'timestamp': time.time(),
                'providers_count': len(self.providers),
                'last_update': self.last_payload.get('timestamp', 0),
                'uptime': time.time() - getattr(self, 'start_time', time.time())
            }
            return web.json_response(health_data)

        # Create web application
        self.app = web.Application()
        self.app.router.add_get('/live', handle_live_data)
        self.app.router.add_get('/providers', handle_provider_stats)
        self.app.router.add_get('/health', handle_health)

        # Add CORS headers for browser access
        async def add_cors_headers(request, handler):
            response = await handler(request)
            response.headers['Access-Control-Allow-Origin'] = '*'
            response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
            response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
            return response

        self.app.middlewares.append(add_cors_headers)

        # Start server
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        site = web.TCPSite(self.runner, host, port)
        await site.start()

        logger.info(f"🌐 [FETCHER] Web API started at http://{host}:{port}")
        logger.info(f"📊 [FETCHER] Endpoints: /live, /providers, /health")

    async def stop_web_api(self):
        """Stop web API server"""
        if self.runner:
            await self.runner.cleanup()
            logger.info("🛑 [FETCHER] Web API stopped")

    async def run_continuous(self, interval: int = 60):
        """Run continuous data fetching"""
        self.start_time = time.time()

        # Start web API
        web_config = self.config.get('web_api', {})
        if web_config.get('enabled', True):
            host = web_config.get('host', '0.0.0.0')
            port = web_config.get('port', 8080)
            await self.serve_web_api(host, port)

        logger.info(f"🔄 [FETCHER] Starting continuous fetching (interval: {interval}s)")

        try:
            while True:
                try:
                    await self.gather_live_data()
                except Exception as e:
                    logger.error(f"❌ [FETCHER] Error in data gathering: {e}")
                    logger.error(traceback.format_exc())

                await asyncio.sleep(interval)
        except KeyboardInterrupt:
            logger.info("⏹️ [FETCHER] Stopping due to keyboard interrupt")
        finally:
            await self.stop_web_api()

    def get_latest_prices(self, symbols: Optional[List[str]] = None) -> Dict[str, float]:
        """Get latest prices for specified symbols or all available"""
        if not self.last_payload or 'live_aggregated' not in self.last_payload:
            return {}

        prices = self.last_payload['live_aggregated']

        if symbols is None:
            return prices

        # Filter for requested symbols
        filtered = {}
        for symbol in symbols:
            # Try exact match first
            if symbol in prices:
                filtered[symbol] = prices[symbol]
                continue

            # Try case-insensitive partial match
            symbol_lower = symbol.lower()
            for key, value in prices.items():
                if symbol_lower in key.lower():
                    filtered[symbol] = value
                    break

        return filtered

    def get_price_momentum(self, symbol: str) -> Optional[float]:
        """Get price momentum for a specific symbol"""
        if not self.last_payload or 'momentum' not in self.last_payload:
            return None

        momentum_key = f"{symbol}_momentum"
        return self.last_payload['momentum'].get(momentum_key)

    def get_data_quality_score(self) -> float:
        """Calculate overall data quality score (0-1)"""
        if not self.last_payload or 'data_quality' not in self.last_payload:
            return 0.0

        quality = self.last_payload['data_quality']
        total_providers = quality.get('total_providers', 1)
        successful_providers = quality.get('successful_providers', 0)

        # Base score on provider success rate
        provider_score = successful_providers / total_providers if total_providers > 0 else 0

        # Adjust based on data points
        data_points = quality.get('total_data_points', 0)
        data_score = min(1.0, data_points / 100)  # Normalize to 100 data points

        # Combined score
        return (provider_score * 0.7 + data_score * 0.3)

# ---------------------- Integration Functions ----------------------

def create_default_fetcher() -> LiveDataFetcher:
    """Create a LiveDataFetcher with default configuration for AutoGPT Trader"""
    config = {
        'coingecko': {
            'enabled': True,
            'ids': ['bitcoin', 'ethereum', 'solana', 'cardano', 'polkadot', 'chainlink'],
            'vs_currencies': ['usd', 'eth', 'btc']
        },
        'binance': {
            'enabled': True,
            'symbols': ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT']
        },
        'bybit': {
            'enabled': True,
            'symbols': ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT']
        },
        'etherscan': {
            'enabled': False,  # Requires API key and specific addresses
            'addresses': []    # Add Ethereum addresses to monitor
        },
        'glassnode': {
            'enabled': False,  # Requires API key
            'metrics': ['market-cap', 'nvt', 'mvrv', 'sopr', 'nupl'],
            'assets': ['BTC', 'ETH']
        },
        'fear_greed': {
            'enabled': True   # No API key required
        },
        'web_api': {
            'enabled': True,
            'host': '0.0.0.0',
            'port': 8080
        },
        'log_dir': 'logs/data_snapshots'
    }

    return LiveDataFetcher(config)

async def main():
    """Main function for standalone execution"""
    logger.info("🚀 Starting LiveDataFetcher...")

    fetcher = create_default_fetcher()

    try:
        await fetcher.run_continuous(interval=30)  # Fetch every 30 seconds
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        logger.error(traceback.format_exc())
    finally:
        await fetcher.stop_web_api()

if __name__ == '__main__':
    asyncio.run(main())
