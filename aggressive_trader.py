#!/usr/bin/env python3
"""
Aggressive Trading System - IMMEDIATE EXECUTION
CRITICAL: This executes real money trades immediately with minimal checks
"""

import os
import sys
import asyncio
import time
from pathlib import Path
from datetime import datetime
from decimal import Decimal

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Force Bybit-only environment
os.environ["BYBIT_ONLY_MODE"] = "true"
os.environ["COINBASE_ENABLED"] = "false"
os.environ["LIVE_TRADING"] = "true"
os.environ["REAL_MONEY_TRADING"] = "true"

async def aggressive_trading():
    """Aggressive trading with immediate execution"""
    # Setup logging to file
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(message)s',
        handlers=[
            logging.FileHandler('aggressive_trading.log'),
            logging.StreamHandler()
        ]
    )

    print("⚡ [AGGRESSIVE] Starting aggressive trading system...")
    logging.info("⚡ [AGGRESSIVE] Starting aggressive trading system...")
    
    try:
        # Import essential components
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Get API credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            raise RuntimeError("CRITICAL: Missing Bybit API credentials")
        
        print("🔧 [AGGRESSIVE] Initializing Bybit client...")
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False  # REAL MONEY TRADING ONLY
        )
        
        if not bybit_client.session:
            raise RuntimeError("CRITICAL: Failed to initialize Bybit client")
        
        print("✅ [AGGRESSIVE] Bybit client initialized successfully")
        
        # Start aggressive trading loop
        cycle_count = 0
        while True:
            try:
                cycle_count += 1
                print(f"\n🎯 [AGGRESSIVE-CYCLE] Cycle {cycle_count} - {datetime.now().strftime('%H:%M:%S')}")
                logging.info(f"🎯 [AGGRESSIVE-CYCLE] Cycle {cycle_count} - {datetime.now().strftime('%H:%M:%S')}")

                # Get current balances
                balances = await bybit_client.get_all_available_balances()
                usdt_balance = balances.get('USDT', 0)

                print(f"💰 [BALANCE] USDT: ${usdt_balance:.2f}")
                logging.info(f"💰 [BALANCE] USDT: ${usdt_balance:.2f}")
                
                if usdt_balance >= 5.0:
                    # Execute aggressive buy
                    await execute_aggressive_buy(bybit_client, usdt_balance)
                else:
                    print(f"⚠️ [BALANCE] Insufficient USDT: ${usdt_balance:.2f} < $5.00")
                
                # Wait 60 seconds between cycles
                print("⏳ [WAIT] Waiting 60 seconds...")
                await asyncio.sleep(60)
                
            except KeyboardInterrupt:
                print("\n🛑 [AGGRESSIVE] Trading stopped by user")
                break
            except Exception as e:
                print(f"❌ [AGGRESSIVE-CYCLE] Error in cycle {cycle_count}: {e}")
                await asyncio.sleep(30)  # Wait 30 seconds on error
                
    except Exception as e:
        print(f"❌ [AGGRESSIVE] Critical error: {e}")
        return 1
    
    return 0

async def execute_aggressive_buy(client, usdt_balance):
    """Execute an aggressive buy order"""
    try:
        # Choose a crypto to buy (rotate between major ones)
        cryptos = ['BTC', 'ETH', 'SOL', 'ADA']
        crypto = cryptos[int(time.time()) % len(cryptos)]
        symbol = f"{crypto}USDT"
        
        print(f"🎯 [BUY] Targeting {crypto}...")
        
        # Get current price
        current_price = await client.get_fast_price(symbol)
        if not current_price or current_price <= 0:
            print(f"❌ [BUY] Failed to get price for {symbol}")
            return
        
        print(f"💹 [PRICE] {symbol}: ${current_price:.4f}")
        
        # Calculate trade amount (30% of USDT balance, max $20)
        trade_amount = min(usdt_balance * 0.3, 20.0)
        
        if trade_amount < 5.0:
            print(f"⚠️ [BUY] Trade amount too small: ${trade_amount:.2f}")
            return
        
        print(f"💰 [BUY] Trade amount: ${trade_amount:.2f}")
        
        # Execute the buy order
        result = await client.place_order(
            symbol=symbol,
            side='Buy',
            order_type='Market',  # Market order for immediate execution
            quantity=None,  # Let the client calculate
            price=None,  # Market price
            amount_usdt=trade_amount
        )
        
        if result and result.get('success'):
            print(f"✅ [BUY] Successfully bought {crypto} for ${trade_amount:.2f}")
            print(f"📋 [ORDER] Order ID: {result.get('order_id', 'N/A')}")
        else:
            print(f"❌ [BUY] Failed to buy {crypto}: {result}")
            
    except Exception as e:
        print(f"❌ [BUY] Error executing buy: {e}")

if __name__ == "__main__":
    print("⚡ [AGGRESSIVE-TRADER] Aggressive trading system starting...")
    print("🎯 [AGGRESSIVE-TRADER] IMMEDIATE EXECUTION - REAL MONEY TRADING")
    print("💰 [AGGRESSIVE-TRADER] Bybit only - Market orders")
    
    try:
        exit_code = asyncio.run(aggressive_trading())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 [AGGRESSIVE-TRADER] System stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ [AGGRESSIVE-TRADER] System failed: {e}")
        sys.exit(1)
