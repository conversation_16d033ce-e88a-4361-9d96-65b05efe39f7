#!/usr/bin/env python3
"""
Real Money Trading Integration Test
Tests the complete trading pipeline using ONLY live data and real exchange APIs
NO MOCK DATA OR SIMULATION MODES ALLOWED
"""

import asyncio
import sys
import os
import logging
from pathlib import Path
from decimal import Decimal
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import validation systems (avoiding config conflicts)
from validation.data_source_auditor import data_source_auditor
from validation.real_money_enforcer import real_money_enforcer
from validation.live_data_monitor import live_data_monitor

logger = logging.getLogger(__name__)

class RealMoneyIntegrationTest:
    """Integration test for real money trading system"""
    
    def __init__(self):
        self.test_results = []
        self.critical_failures = []
        
    async def run_comprehensive_test(self):
        """Run comprehensive real money trading integration test"""
        print("🔍 [TEST] Starting Real Money Trading Integration Test")
        print("=" * 80)
        
        try:
            # Test 1: Configuration Validation
            await self._test_configuration_validation()
            
            # Test 2: Data Source Compliance
            await self._test_data_source_compliance()
            
            # Test 3: Exchange API Connectivity (Live Only)
            await self._test_live_exchange_connectivity()
            
            # Test 4: Real Balance Validation
            await self._test_real_balance_validation()
            
            # Test 5: Live Price Data Validation
            await self._test_live_price_data()
            
            # Test 6: Trading Operation Validation
            await self._test_trading_operation_validation()
            
            # Test 7: Monitoring System Validation
            await self._test_monitoring_system()
            
            # Generate test report
            self._generate_test_report()
            
            return len(self.critical_failures) == 0
            
        except Exception as e:
            self.critical_failures.append(f"Test suite failed: {e}")
            logger.error(f"❌ [TEST] Integration test failed: {e}")
            return False

    async def _test_configuration_validation(self):
        """Test 1: Validate real money trading configuration"""
        print("\n🔧 [TEST-1] Configuration Validation")

        try:
            # Test environment variables for real money trading
            import os

            # Check that live trading is enabled
            live_trading = os.getenv('LIVE_TRADING', '').lower() == 'true'
            real_money = os.getenv('REAL_MONEY_TRADING', '').lower() == 'true'
            environment = os.getenv('ENVIRONMENT', '').lower() == 'production'

            if live_trading and real_money and environment:
                self.test_results.append("✅ Configuration validation: PASSED")
                print("✅ Environment configured for real money trading")
            else:
                self.critical_failures.append("Configuration validation failed - environment not set for real money trading")
                print("❌ Configuration validation FAILED - environment not set for real money trading")
                print(f"  LIVE_TRADING: {live_trading}")
                print(f"  REAL_MONEY_TRADING: {real_money}")
                print(f"  ENVIRONMENT: {environment}")

            # Check that no simulation modes are enabled
            simulation_vars = ['DEMO_MODE', 'TEST_MODE', 'SIMULATION_MODE', 'DRY_RUN', 'SANDBOX_MODE']
            simulation_detected = False

            for var in simulation_vars:
                value = os.getenv(var, '').lower()
                if value in ['true', '1', 'yes', 'on']:
                    simulation_detected = True
                    print(f"❌ Simulation mode detected: {var}={value}")

            if not simulation_detected:
                self.test_results.append("✅ No simulation modes detected: PASSED")
                print("✅ No simulation modes detected")
            else:
                self.critical_failures.append("Simulation modes detected")

        except Exception as e:
            self.critical_failures.append(f"Configuration test failed: {e}")
            print(f"❌ Configuration test failed: {e}")

    async def _test_data_source_compliance(self):
        """Test 2: Validate data source compliance"""
        print("\n🔍 [TEST-2] Data Source Compliance")
        
        try:
            # Run data source audit
            audit_result = await data_source_auditor.audit_codebase()
            
            if audit_result.is_compliant:
                self.test_results.append("✅ Data source compliance: PASSED")
                print("✅ All data sources are compliant with real money trading")
            else:
                self.critical_failures.append(f"Data source compliance failed: {audit_result.critical_violations} critical violations")
                print(f"❌ Data source compliance FAILED: {audit_result.critical_violations} critical violations")
                
                # Show top 3 critical violations
                critical_violations = [v for v in audit_result.violations_found if v.severity == 'CRITICAL'][:3]
                for violation in critical_violations:
                    print(f"  - {violation.file_path}:{violation.line_number} - {violation.description}")
            
        except Exception as e:
            self.critical_failures.append(f"Data source compliance test failed: {e}")
            print(f"❌ Data source compliance test failed: {e}")

    async def _test_live_exchange_connectivity(self):
        """Test 3: Test live exchange API connectivity"""
        print("\n🌐 [TEST-3] Live Exchange Connectivity")
        
        try:
            # Test Bybit connectivity
            try:
                from src.exchanges.bybit_client_fixed import BybitClientFixed
                bybit_client = BybitClientFixed()
                
                # Test real API call
                trading_pairs = bybit_client.get_all_trading_pairs()
                if trading_pairs and 'trading_pairs' in trading_pairs:
                    self.test_results.append("✅ Bybit live connectivity: PASSED")
                    print(f"✅ Bybit connected - {len(trading_pairs['trading_pairs'])} trading pairs available")
                else:
                    self.critical_failures.append("Bybit connectivity failed")
                    print("❌ Bybit connectivity failed")
                    
            except Exception as e:
                self.critical_failures.append(f"Bybit connectivity failed: {e}")
                print(f"❌ Bybit connectivity failed: {e}")
            
            # Test Coinbase connectivity (if available)
            try:
                from src.exchanges.coinbase_enhanced_client import CoinbaseEnhancedClient
                coinbase_client = CoinbaseEnhancedClient()
                
                # Test authentication
                if hasattr(coinbase_client, 'test_authentication'):
                    auth_result = await coinbase_client.test_authentication()
                    if auth_result:
                        self.test_results.append("✅ Coinbase live connectivity: PASSED")
                        print("✅ Coinbase connected and authenticated")
                    else:
                        print("⚠️ Coinbase authentication failed (expected due to API restrictions)")
                else:
                    print("⚠️ Coinbase authentication test not available")
                    
            except Exception as e:
                print(f"⚠️ Coinbase connectivity test failed: {e} (expected due to API restrictions)")
            
        except Exception as e:
            self.critical_failures.append(f"Exchange connectivity test failed: {e}")
            print(f"❌ Exchange connectivity test failed: {e}")

    async def _test_real_balance_validation(self):
        """Test 4: Validate real balance data"""
        print("\n💰 [TEST-4] Real Balance Validation")
        
        try:
            # Test balance validation with real data
            test_operation = {
                'balance_data': {'USDT': 100.0, 'BTC': 0.001},
                'balance_timestamp': asyncio.get_event_loop().time()
            }
            
            validation_result = await real_money_enforcer.validate_trading_operation(
                'balance_test', 'bybit', test_operation
            )
            
            if validation_result.is_valid:
                self.test_results.append("✅ Real balance validation: PASSED")
                print("✅ Balance validation system working correctly")
            else:
                self.critical_failures.append(f"Balance validation failed: {validation_result.error_message}")
                print(f"❌ Balance validation failed: {validation_result.error_message}")
                
        except Exception as e:
            self.critical_failures.append(f"Balance validation test failed: {e}")
            print(f"❌ Balance validation test failed: {e}")

    async def _test_live_price_data(self):
        """Test 5: Validate live price data"""
        print("\n📊 [TEST-5] Live Price Data Validation")
        
        try:
            # Test live price fetching
            from src.exchanges.bybit_client_fixed import BybitClientFixed
            bybit_client = BybitClientFixed()
            
            # Test getting real price for BTCUSDT
            try:
                btc_price = bybit_client.get_price('BTCUSDT')
                if btc_price and btc_price > 0:
                    self.test_results.append("✅ Live price data: PASSED")
                    print(f"✅ Live price data working - BTCUSDT: ${btc_price:,.2f}")
                else:
                    self.critical_failures.append("Live price data failed - no valid price returned")
                    print("❌ Live price data failed - no valid price returned")
                    
            except Exception as e:
                self.critical_failures.append(f"Live price data failed: {e}")
                print(f"❌ Live price data failed: {e}")
                
        except Exception as e:
            self.critical_failures.append(f"Price data test failed: {e}")
            print(f"❌ Price data test failed: {e}")

    async def _test_trading_operation_validation(self):
        """Test 6: Validate trading operation enforcement"""
        print("\n🛡️ [TEST-6] Trading Operation Validation")
        
        try:
            # Test valid trading operation
            valid_operation = {
                'api_url': 'https://api.bybit.com',
                'balance_data': {'USDT': 100.0},
                'order_data': {'symbol': 'BTCUSDT', 'amount': 0.001},
                'balance_timestamp': asyncio.get_event_loop().time()
            }
            
            validation_result = await real_money_enforcer.validate_trading_operation(
                'test_order', 'bybit', valid_operation
            )
            
            if validation_result.is_valid:
                self.test_results.append("✅ Trading operation validation: PASSED")
                print("✅ Trading operation validation working correctly")
            else:
                self.critical_failures.append(f"Trading operation validation failed: {validation_result.error_message}")
                print(f"❌ Trading operation validation failed: {validation_result.error_message}")
            
            # Test invalid operation (should fail)
            invalid_operation = {
                'api_url': 'https://testnet.bybit.com',  # Prohibited endpoint
                'balance_data': {'USDT': 100.0},
                'order_data': {'symbol': 'BTCUSDT', 'amount': 0.001}
            }
            
            try:
                invalid_validation = await real_money_enforcer.validate_trading_operation(
                    'invalid_test', 'bybit', invalid_operation
                )
                
                if not invalid_validation.is_valid:
                    self.test_results.append("✅ Invalid operation detection: PASSED")
                    print("✅ Invalid operation correctly detected and blocked")
                else:
                    self.critical_failures.append("Invalid operation was not detected")
                    print("❌ Invalid operation was not detected")
                    
            except Exception as e:
                # This is expected - invalid operations should raise exceptions
                self.test_results.append("✅ Invalid operation detection: PASSED")
                print("✅ Invalid operation correctly blocked with exception")
                
        except Exception as e:
            self.critical_failures.append(f"Trading operation validation test failed: {e}")
            print(f"❌ Trading operation validation test failed: {e}")

    async def _test_monitoring_system(self):
        """Test 7: Validate monitoring system"""
        print("\n📡 [TEST-7] Monitoring System Validation")
        
        try:
            # Test live data monitor
            live_data_monitor.register_data_source('test_source')
            
            # Record a live data request
            live_data_monitor.record_data_request('test_source', is_live=True, latency_ms=50.0)
            
            # Get monitoring summary
            summary = live_data_monitor.get_monitoring_summary()
            
            if summary['monitoring_active'] is not None:
                self.test_results.append("✅ Monitoring system: PASSED")
                print("✅ Monitoring system is functional")
            else:
                self.critical_failures.append("Monitoring system failed")
                print("❌ Monitoring system failed")
                
            live_data_monitor.unregister_data_source('test_source')
            
        except Exception as e:
            self.critical_failures.append(f"Monitoring system test failed: {e}")
            print(f"❌ Monitoring system test failed: {e}")

    def _generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 80)
        print("REAL MONEY TRADING INTEGRATION TEST REPORT")
        print("=" * 80)
        
        total_tests = len(self.test_results) + len(self.critical_failures)
        passed_tests = len(self.test_results)
        failed_tests = len(self.critical_failures)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests / total_tests * 100):.1f}%" if total_tests > 0 else "0%")
        
        if self.test_results:
            print("\n✅ PASSED TESTS:")
            for result in self.test_results:
                print(f"  {result}")
        
        if self.critical_failures:
            print("\n❌ FAILED TESTS:")
            for failure in self.critical_failures:
                print(f"  {failure}")
        
        print("\n" + "=" * 80)
        
        if len(self.critical_failures) == 0:
            print("🎉 ALL TESTS PASSED - SYSTEM READY FOR REAL MONEY TRADING")
        else:
            print("⚠️ CRITICAL FAILURES DETECTED - FIX BEFORE LIVE TRADING")
        
        print("=" * 80)

async def main():
    """Run the integration test"""
    test_suite = RealMoneyIntegrationTest()
    success = await test_suite.run_comprehensive_test()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
